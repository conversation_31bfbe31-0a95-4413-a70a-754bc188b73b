import { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import { VideoPlayer } from '../video/VideoPlayer';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { 
  XMarkIcon, 
  ArrowDownTrayIcon,
  EyeIcon,
  InformationCircleIcon,
  PhotoIcon,
  VideoCameraIcon
} from '@heroicons/react/24/outline';

interface Asset {
  id: string;
  name: string;
  type: 'image' | 'video' | 'timelapse';
  file_path: string;
  file_size: number;
  mime_type: string;
  metadata: any;
  status: string;
  visibility: string;
  created_at: string;
  created_by_name: string;
  thumbnails?: {
    small: string;
    medium: string;
    large: string;
  };
}

interface AssetPreviewProps {
  asset: Asset;
  onClose: () => void;
}

export function AssetPreview({ asset, onClose }: AssetPreviewProps) {
  const { token } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [downloadUrl, setDownloadUrl] = useState<string>('');
  const [thumbnailUrl, setThumbnailUrl] = useState<string>('');
  const [error, setError] = useState<string>('');

  useEffect(() => {
    fetchAssetUrls();
  }, [asset.id]);

  const fetchAssetUrls = async () => {
    try {
      const response = await fetch(`/api/assets/${asset.id}/urls`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPreviewUrl(data.previewUrl);
        setDownloadUrl(data.downloadUrl);
        setThumbnailUrl(data.thumbnailUrl);
      } else {
        setError('Failed to load asset preview');
      }
    } catch (err) {
      console.error('Failed to fetch asset URLs:', err);
      setError('Failed to load asset preview');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = () => {
    if (downloadUrl) {
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = asset.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getAssetIcon = () => {
    switch (asset.type) {
      case 'image':
        return <PhotoIcon className="h-6 w-6 text-blue-500" />;
      case 'video':
      case 'timelapse':
        return <VideoCameraIcon className="h-6 w-6 text-green-500" />;
      default:
        return <PhotoIcon className="h-6 w-6 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getVisibilityColor = (visibility: string) => {
    return visibility === 'public' 
      ? 'bg-green-100 text-green-800'
      : 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white min-h-[90vh]">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            {getAssetIcon()}
            <div>
              <h3 className="text-lg font-medium text-gray-900">{asset.name}</h3>
              <div className="flex items-center space-x-2 mt-1">
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(asset.status)}`}>
                  {asset.status}
                </span>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getVisibilityColor(asset.visibility)}`}>
                  {asset.visibility}
                </span>
                <span className="text-xs text-gray-500 capitalize">
                  {asset.type}
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={handleDownload}
              disabled={!downloadUrl}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              Download
            </button>
            
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <InformationCircleIcon className="mx-auto h-12 w-12 text-red-400" />
            <h3 className="mt-2 text-sm font-semibold text-gray-900">Preview Error</h3>
            <p className="mt-1 text-sm text-gray-500">{error}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Preview */}
            <div className="lg:col-span-2">
              <div className="bg-gray-100 rounded-lg overflow-hidden">
                {asset.type === 'image' ? (
                  <div className="aspect-video flex items-center justify-center">
                    {previewUrl ? (
                      <img
                        src={previewUrl}
                        alt={asset.name}
                        className="max-w-full max-h-full object-contain"
                        onError={() => setError('Failed to load image')}
                      />
                    ) : thumbnailUrl ? (
                      <img
                        src={thumbnailUrl}
                        alt={asset.name}
                        className="max-w-full max-h-full object-contain"
                        onError={() => setError('Failed to load thumbnail')}
                      />
                    ) : (
                      <div className="text-center">
                        <PhotoIcon className="mx-auto h-16 w-16 text-gray-400" />
                        <p className="mt-2 text-sm text-gray-500">No preview available</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="aspect-video">
                    {previewUrl ? (
                      <VideoPlayer
                        src={previewUrl}
                        poster={thumbnailUrl}
                        title={asset.name}
                        downloadUrl={downloadUrl}
                        className="w-full h-full"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <div className="text-center">
                          <VideoCameraIcon className="mx-auto h-16 w-16 text-gray-400" />
                          <p className="mt-2 text-sm text-gray-500">No preview available</p>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Details */}
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Asset Details</h4>
                <dl className="space-y-2 text-sm">
                  <div>
                    <dt className="text-gray-500">File Size:</dt>
                    <dd className="text-gray-900 font-medium">{formatFileSize(asset.file_size)}</dd>
                  </div>
                  <div>
                    <dt className="text-gray-500">MIME Type:</dt>
                    <dd className="text-gray-900 font-medium">{asset.mime_type}</dd>
                  </div>
                  <div>
                    <dt className="text-gray-500">Created:</dt>
                    <dd className="text-gray-900 font-medium">{formatDate(asset.created_at)}</dd>
                  </div>
                  {asset.created_by_name && (
                    <div>
                      <dt className="text-gray-500">Created By:</dt>
                      <dd className="text-gray-900 font-medium">{asset.created_by_name}</dd>
                    </div>
                  )}
                </dl>
              </div>

              {/* Metadata */}
              {asset.metadata && Object.keys(asset.metadata).length > 0 && (
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Metadata</h4>
                  <dl className="space-y-2 text-sm">
                    {Object.entries(asset.metadata).map(([key, value]) => (
                      <div key={key}>
                        <dt className="text-gray-500 capitalize">{key.replace(/_/g, ' ')}:</dt>
                        <dd className="text-gray-900 font-medium">
                          {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                        </dd>
                      </div>
                    ))}
                  </dl>
                </div>
              )}

              {/* Thumbnails */}
              {asset.thumbnails && (
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Thumbnails</h4>
                  <div className="grid grid-cols-3 gap-2">
                    {Object.entries(asset.thumbnails).map(([size, url]) => (
                      <div key={size} className="text-center">
                        <div className="aspect-square bg-gray-100 rounded overflow-hidden mb-1">
                          <img
                            src={url}
                            alt={`${size} thumbnail`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <span className="text-xs text-gray-500 capitalize">{size}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Actions</h4>
                <div className="space-y-2">
                  <button
                    onClick={handleDownload}
                    disabled={!downloadUrl}
                    className="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                  >
                    <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                    Download Original
                  </button>
                  
                  {previewUrl && (
                    <button
                      onClick={() => window.open(previewUrl, '_blank')}
                      className="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      <EyeIcon className="h-4 w-4 mr-2" />
                      Open in New Tab
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
