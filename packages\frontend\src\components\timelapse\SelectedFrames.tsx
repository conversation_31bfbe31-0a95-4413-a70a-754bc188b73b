import { useState } from 'react';
import { 
  XMarkIcon, 
  ArrowUpIcon, 
  ArrowDownIcon,
  PhotoIcon,
  PlayIcon
} from '@heroicons/react/24/outline';

interface Asset {
  id: string;
  name: string;
  type: string;
  file_path: string;
  file_size: number;
  metadata: any;
  created_at: string;
}

interface SelectedFramesProps {
  selectedAssets: Asset[];
  onReorder: (dragIndex: number, hoverIndex: number) => void;
  onRemove: (assetId: string) => void;
}

export function SelectedFrames({ selectedAssets, onReorder, onRemove }: SelectedFramesProps) {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (draggedIndex !== null && draggedIndex !== dropIndex) {
      onReorder(draggedIndex, dropIndex);
    }
    setDraggedIndex(null);
  };

  const moveUp = (index: number) => {
    if (index > 0) {
      onReorder(index, index - 1);
    }
  };

  const moveDown = (index: number) => {
    if (index < selectedAssets.length - 1) {
      onReorder(index, index + 1);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const estimatedDuration = (fps: number = 30) => {
    return (selectedAssets.length / fps).toFixed(1);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-lg font-medium text-gray-900">Selected Frames</h4>
        {selectedAssets.length > 0 && (
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <PlayIcon className="h-4 w-4" />
            <span>~{estimatedDuration()}s @ 30fps</span>
          </div>
        )}
      </div>

      {selectedAssets.length === 0 ? (
        <div className="text-center py-8">
          <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-500">
            No frames selected
          </p>
          <p className="text-xs text-gray-400 mt-1">
            Click on images to add them to your timelapse
          </p>
        </div>
      ) : (
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {selectedAssets.map((asset, index) => (
            <div
              key={asset.id}
              draggable
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, index)}
              className={`flex items-center space-x-3 p-2 rounded-lg border transition-all cursor-move ${
                draggedIndex === index
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              {/* Frame number */}
              <div className="flex-shrink-0 w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">
                  {index + 1}
                </span>
              </div>

              {/* Thumbnail placeholder */}
              <div className="flex-shrink-0 w-12 h-12 bg-gray-100 rounded overflow-hidden">
                <div className="w-full h-full flex items-center justify-center">
                  <PhotoIcon className="h-6 w-6 text-gray-400" />
                </div>
              </div>

              {/* Asset info */}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {asset.name}
                </p>
                <p className="text-xs text-gray-500">
                  {formatDate(asset.created_at)}
                </p>
              </div>

              {/* Controls */}
              <div className="flex-shrink-0 flex items-center space-x-1">
                <button
                  onClick={() => moveUp(index)}
                  disabled={index === 0}
                  className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Move up"
                >
                  <ArrowUpIcon className="h-4 w-4" />
                </button>
                
                <button
                  onClick={() => moveDown(index)}
                  disabled={index === selectedAssets.length - 1}
                  className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Move down"
                >
                  <ArrowDownIcon className="h-4 w-4" />
                </button>
                
                <button
                  onClick={() => onRemove(asset.id)}
                  className="p-1 text-gray-400 hover:text-red-600"
                  title="Remove"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Summary */}
      {selectedAssets.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Total Frames:</span>
              <span className="ml-2 font-medium">{selectedAssets.length}</span>
            </div>
            <div>
              <span className="text-gray-500">Duration:</span>
              <span className="ml-2 font-medium">~{estimatedDuration()}s</span>
            </div>
          </div>
          
          {selectedAssets.length < 2 && (
            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-700">
              Select at least 2 frames to create a timelapse
            </div>
          )}
          
          {selectedAssets.length >= 2 && (
            <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-xs text-green-700">
              Ready to create timelapse with {selectedAssets.length} frames
            </div>
          )}
        </div>
      )}

      {/* Quick Actions */}
      {selectedAssets.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex justify-between text-xs">
            <button
              onClick={() => {
                // Sort by date (oldest first)
                const sorted = [...selectedAssets].sort((a, b) => 
                  new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
                );
                sorted.forEach((asset, index) => {
                  const currentIndex = selectedAssets.findIndex(a => a.id === asset.id);
                  if (currentIndex !== index) {
                    onReorder(currentIndex, index);
                  }
                });
              }}
              className="text-primary-600 hover:text-primary-700 font-medium"
            >
              Sort by Date
            </button>
            
            <button
              onClick={() => {
                // Reverse order
                for (let i = 0; i < Math.floor(selectedAssets.length / 2); i++) {
                  const j = selectedAssets.length - 1 - i;
                  onReorder(i, j);
                }
              }}
              className="text-primary-600 hover:text-primary-700 font-medium"
            >
              Reverse Order
            </button>
            
            <button
              onClick={() => {
                selectedAssets.forEach(asset => onRemove(asset.id));
              }}
              className="text-red-600 hover:text-red-700 font-medium"
            >
              Clear All
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
