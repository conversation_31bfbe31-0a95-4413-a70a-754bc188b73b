import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { UserForm } from '../users/UserForm';

// Mock the stores
vi.mock('../../stores/authStore', () => ({
  useAuthStore: () => ({
    token: 'mock-token',
  })
}));

vi.mock('../../stores/toastStore', () => ({
  useToastStore: () => ({
    success: vi.fn(),
    error: vi.fn(),
  })
}));

// Mock fetch
global.fetch = vi.fn();

const mockTenants = [
  { id: '1', name: 'tenant-1', is_active: true },
  { id: '2', name: 'tenant-2', is_active: true },
];

const mockUser = {
  id: '1',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'client' as const,
  tenant_id: '1',
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  tenant_name: 'tenant-1'
};

describe('UserForm', () => {
  const mockOnClose = vi.fn();
  const mockOnSave = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (global.fetch as any)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ tenants: mockTenants })
      });
  });

  it('renders create user form', async () => {
    render(
      <UserForm
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    expect(screen.getByText('Add User')).toBeInTheDocument();
    expect(screen.getByLabelText(/Full Name/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Email Address/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Role/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Password/)).toBeInTheDocument();
  });

  it('renders edit user form with existing data', async () => {
    render(
      <UserForm
        user={mockUser}
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    expect(screen.getByText('Edit User')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test User')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
  });

  it('loads tenants on mount', async () => {
    render(
      <UserForm
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/tenants', {
        headers: { 'Authorization': 'Bearer mock-token' }
      });
    });
  });

  it('validates required fields', async () => {
    render(
      <UserForm
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    const submitButton = screen.getByText('Create User');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Name is required')).toBeInTheDocument();
      expect(screen.getByText('Invalid email address')).toBeInTheDocument();
    });
  });

  it('validates email format', async () => {
    render(
      <UserForm
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    const emailInput = screen.getByLabelText(/Email Address/);
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.blur(emailInput);

    await waitFor(() => {
      expect(screen.getByText('Invalid email address')).toBeInTheDocument();
    });
  });

  it('validates password length for new users', async () => {
    render(
      <UserForm
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    const passwordInput = screen.getByLabelText(/Password/);
    fireEvent.change(passwordInput, { target: { value: '123' } });
    fireEvent.blur(passwordInput);

    await waitFor(() => {
      expect(screen.getByText('Password must be at least 8 characters')).toBeInTheDocument();
    });
  });

  it('generates password when generate button is clicked', async () => {
    render(
      <UserForm
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    const generateButton = screen.getByText('Generate');
    fireEvent.click(generateButton);

    const passwordInput = screen.getByLabelText(/Password/) as HTMLInputElement;
    expect(passwordInput.value).toHaveLength(12);
    expect(passwordInput.type).toBe('text'); // Should show password after generation
  });

  it('toggles password visibility', async () => {
    render(
      <UserForm
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    const passwordInput = screen.getByLabelText(/Password/) as HTMLInputElement;
    const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });

    expect(passwordInput.type).toBe('password');
    
    fireEvent.click(toggleButton);
    expect(passwordInput.type).toBe('text');
    
    fireEvent.click(toggleButton);
    expect(passwordInput.type).toBe('password');
  });

  it('shows user preview when data is entered', async () => {
    render(
      <UserForm
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    // Wait for tenants to load and auto-select first one
    await waitFor(() => {
      expect(screen.getByText('User Preview')).toBeInTheDocument();
    });

    const nameInput = screen.getByLabelText(/Full Name/);
    const emailInput = screen.getByLabelText(/Email Address/);

    fireEvent.change(nameInput, { target: { value: 'John Doe' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
  });

  it('submits form with valid data', async () => {
    (global.fetch as any)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ tenants: mockTenants })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ user: { ...mockUser, id: 'new-id' } })
      });

    render(
      <UserForm
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    // Fill form
    await waitFor(() => {
      const nameInput = screen.getByLabelText(/Full Name/);
      const emailInput = screen.getByLabelText(/Email Address/);
      const passwordInput = screen.getByLabelText(/Password/);

      fireEvent.change(nameInput, { target: { value: 'John Doe' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
    });

    const submitButton = screen.getByText('Create User');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/users', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer mock-token',
          'Content-Type': 'application/json',
        },
        body: expect.stringContaining('<EMAIL>')
      });
    });

    expect(mockOnSave).toHaveBeenCalled();
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('handles API errors', async () => {
    const mockError = vi.fn();
    vi.mocked(require('../../stores/toastStore').useToastStore).mockReturnValue({
      success: vi.fn(),
      error: mockError,
    });

    (global.fetch as any)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ tenants: mockTenants })
      })
      .mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({ error: 'Email already exists' })
      });

    render(
      <UserForm
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    // Fill and submit form
    await waitFor(() => {
      const nameInput = screen.getByLabelText(/Full Name/);
      const emailInput = screen.getByLabelText(/Email Address/);
      const passwordInput = screen.getByLabelText(/Password/);

      fireEvent.change(nameInput, { target: { value: 'John Doe' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
    });

    const submitButton = screen.getByText('Create User');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockError).toHaveBeenCalledWith('Failed to Save', 'Email already exists');
    });
  });

  it('closes form when cancel is clicked', () => {
    render(
      <UserForm
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('closes form when X button is clicked', () => {
    render(
      <UserForm
        onClose={mockOnClose}
        onSave={mockOnSave}
      />
    );

    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });
});
