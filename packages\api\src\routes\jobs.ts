import { Router } from 'express';
import { z } from 'zod';
import crypto from 'crypto';
import { asyncH<PERSON><PERSON>, createError } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole } from '../middleware/auth';
import { query } from '../database/connection';
import { logger } from '../utils/logger';
import {
  scheduleTimelapseJob,
  scheduleThumbnailJob,
  scheduleFtpSyncJob,
  getJobStatus,
  cancelJob,
  retryJob
} from '../utils/jobQueue';

const router = Router();

const createJobSchema = z.object({
  type: z.enum(['timelapse', 'thumbnail', 'ftp-sync']),
  payload: z.object({}).optional(),
  assetIds: z.array(z.string().uuid()).optional(),
});

const createTimelapseJobSchema = z.object({
  assetIds: z.array(z.string().uuid()).min(2),
  settings: z.object({
    fps: z.number().min(1).max(60).default(30),
    quality: z.enum(['high', 'medium', 'low']).default('medium'),
    resolution: z.enum(['4k', '1080p', '720p']).default('1080p'),
    outputName: z.string().min(1).max(100),
  }),
});

const createThumbnailJobSchema = z.object({
  assetId: z.string().uuid(),
  sizes: z.array(z.object({
    name: z.string(),
    width: z.number().min(50).max(2000),
    height: z.number().min(50).max(2000),
    quality: z.number().min(10).max(100).default(85),
  })).optional(),
});

const createFtpSyncJobSchema = z.object({
  ftpSourceId: z.string().uuid(),
  syncPath: z.string().optional(),
});

// Get jobs (filtered by tenant)
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { type, status, page = '1', limit = '20' } = req.query;
  
  let queryText = `
    SELECT j.id, j.type, j.status, j.payload, j.result, j.error_message, 
           j.progress, j.started_at, j.completed_at, j.created_at, j.updated_at,
           u.name as created_by_name
    FROM jobs j
    LEFT JOIN users u ON j.created_by = u.id
    WHERE j.tenant_id = $1
  `;
  let queryParams = [req.user?.tenantId];
  let paramCount = 2;

  if (type) {
    queryText += ` AND j.type = $${paramCount++}`;
    queryParams.push(type);
  }

  if (status) {
    queryText += ` AND j.status = $${paramCount++}`;
    queryParams.push(status);
  }

  queryText += ` ORDER BY j.created_at DESC`;

  // Pagination
  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const offset = (pageNum - 1) * limitNum;

  queryText += ` LIMIT $${paramCount++} OFFSET $${paramCount++}`;
  queryParams.push(limitNum, offset);

  const result = await query(queryText, queryParams);

  // Get total count
  let countQuery = `
    SELECT COUNT(*) as total
    FROM jobs j
    WHERE j.tenant_id = $1
  `;
  let countParams = [req.user?.tenantId];
  let countParamCount = 2;

  if (type) {
    countQuery += ` AND j.type = $${countParamCount++}`;
    countParams.push(type);
  }

  if (status) {
    countQuery += ` AND j.status = $${countParamCount++}`;
    countParams.push(status);
  }

  const countResult = await query(countQuery, countParams);
  const total = parseInt(countResult.rows[0].total);

  res.json({
    jobs: result.rows,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      pages: Math.ceil(total / limitNum),
    }
  });
}));

// Get job by ID
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  
  const result = await query(
    `SELECT j.id, j.type, j.status, j.payload, j.result, j.error_message, 
            j.progress, j.started_at, j.completed_at, j.created_at, j.updated_at,
            u.name as created_by_name
     FROM jobs j
     LEFT JOIN users u ON j.created_by = u.id
     WHERE j.id = $1 AND j.tenant_id = $2`,
    [id, req.user?.tenantId]
  );

  if (result.rows.length === 0) {
    throw createError('Job not found', 404);
  }

  // Get associated assets
  const assetsResult = await query(
    `SELECT a.id, a.name, a.type, a.file_path
     FROM assets a
     JOIN job_assets ja ON a.id = ja.asset_id
     WHERE ja.job_id = $1`,
    [id]
  );

  res.json({
    job: {
      ...result.rows[0],
      assets: assetsResult.rows
    }
  });
}));

// Create timelapse job (admin only)
router.post('/timelapse', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createTimelapseJobSchema.parse(req.body);

  // Validate asset IDs
  const assetsResult = await query(
    `SELECT id FROM assets WHERE id = ANY($1) AND tenant_id = $2 AND type = 'image'`,
    [validatedData.assetIds, req.user?.tenantId]
  );

  if (assetsResult.rows.length !== validatedData.assetIds.length) {
    throw createError('One or more image assets not found', 404);
  }

  const jobId = crypto.randomUUID();

  // Create job record
  const jobResult = await query(
    `INSERT INTO jobs (id, tenant_id, type, payload, created_by)
     VALUES ($1, $2, 'timelapse', $3, $4)
     RETURNING id, type, status, payload, progress, created_at`,
    [
      jobId,
      req.user?.tenantId,
      JSON.stringify({ assetIds: validatedData.assetIds, settings: validatedData.settings }),
      req.user?.id
    ]
  );

  const job = jobResult.rows[0];

  // Associate assets with job
  const jobAssetValues = validatedData.assetIds.map(assetId => `('${jobId}', '${assetId}')`).join(', ');
  await query(`INSERT INTO job_assets (job_id, asset_id) VALUES ${jobAssetValues}`);

  // Schedule job in queue
  await scheduleTimelapseJob({
    jobId,
    tenantId: req.user?.tenantId!,
    assetIds: validatedData.assetIds,
    settings: validatedData.settings
  });

  logger.info(`Timelapse job created: ${jobId} by user ${req.user?.id}`);

  res.status(201).json({ job });
}));

// Create thumbnail job (admin only)
router.post('/thumbnail', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createThumbnailJobSchema.parse(req.body);

  // Validate asset
  const assetResult = await query(
    `SELECT id FROM assets WHERE id = $1 AND tenant_id = $2 AND type = 'image'`,
    [validatedData.assetId, req.user?.tenantId]
  );

  if (assetResult.rows.length === 0) {
    throw createError('Image asset not found', 404);
  }

  const jobId = crypto.randomUUID();

  // Default thumbnail sizes if not provided
  const defaultSizes = [
    { name: 'thumb', width: 150, height: 150, quality: 80 },
    { name: 'medium', width: 400, height: 300, quality: 85 },
    { name: 'large', width: 800, height: 600, quality: 90 }
  ];

  const sizes = validatedData.sizes || defaultSizes;

  // Create job record
  const jobResult = await query(
    `INSERT INTO jobs (id, tenant_id, type, payload, created_by)
     VALUES ($1, $2, 'thumbnail', $3, $4)
     RETURNING id, type, status, payload, progress, created_at`,
    [
      jobId,
      req.user?.tenantId,
      JSON.stringify({ assetId: validatedData.assetId, sizes }),
      req.user?.id
    ]
  );

  const job = jobResult.rows[0];

  // Associate asset with job
  await query(`INSERT INTO job_assets (job_id, asset_id) VALUES ($1, $2)`, [jobId, validatedData.assetId]);

  // Schedule job in queue
  await scheduleThumbnailJob({
    jobId,
    tenantId: req.user?.tenantId!,
    assetId: validatedData.assetId,
    sizes
  });

  logger.info(`Thumbnail job created: ${jobId} by user ${req.user?.id}`);

  res.status(201).json({ job });
}));

// Create FTP sync job (admin only)
router.post('/ftp-sync', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createFtpSyncJobSchema.parse(req.body);

  // Validate FTP source
  const ftpSourceResult = await query(
    `SELECT id FROM ftp_sources WHERE id = $1 AND tenant_id = $2 AND is_active = true`,
    [validatedData.ftpSourceId, req.user?.tenantId]
  );

  if (ftpSourceResult.rows.length === 0) {
    throw createError('FTP source not found or inactive', 404);
  }

  const jobId = crypto.randomUUID();

  // Create job record
  const jobResult = await query(
    `INSERT INTO jobs (id, tenant_id, type, payload, created_by)
     VALUES ($1, $2, 'ftp-sync', $3, $4)
     RETURNING id, type, status, payload, progress, created_at`,
    [
      jobId,
      req.user?.tenantId,
      JSON.stringify({ ftpSourceId: validatedData.ftpSourceId, syncPath: validatedData.syncPath }),
      req.user?.id
    ]
  );

  const job = jobResult.rows[0];

  // Schedule job in queue
  await scheduleFtpSyncJob({
    jobId,
    tenantId: req.user?.tenantId!,
    ftpSourceId: validatedData.ftpSourceId,
    syncPath: validatedData.syncPath
  });

  logger.info(`FTP sync job created: ${jobId} by user ${req.user?.id}`);

  res.status(201).json({ job });
}));

// Cancel job
router.post('/:id/cancel', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if job exists and belongs to user's tenant
  const existingJob = await query(
    'SELECT id, status FROM jobs WHERE id = $1 AND tenant_id = $2',
    [id, req.user?.tenantId]
  );

  if (existingJob.rows.length === 0) {
    throw createError('Job not found', 404);
  }

  const job = existingJob.rows[0];

  if (job.status === 'completed' || job.status === 'failed') {
    throw createError('Cannot cancel completed or failed job', 409);
  }

  // Update job status
  const result = await query(
    `UPDATE jobs SET status = 'failed', error_message = 'Cancelled by user', completed_at = NOW() 
     WHERE id = $1 
     RETURNING id, type, status, error_message, completed_at`,
    [id]
  );

  // Cancel job in BullMQ queue
  const jobType = job.type;
  try {
    await cancelJob(jobType, id);
  } catch (queueError) {
    logger.warn(`Failed to cancel job in queue: ${queueError.message}`);
  }

  logger.info(`Job cancelled: ${id} by user ${req.user?.id}`);

  res.json({
    job: result.rows[0]
  });
}));

// Retry job
router.post('/:id/retry', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if job exists and belongs to user's tenant
  const existingJob = await query(
    'SELECT id, status, type, payload FROM jobs WHERE id = $1 AND tenant_id = $2',
    [id, req.user?.tenantId]
  );

  if (existingJob.rows.length === 0) {
    throw createError('Job not found', 404);
  }

  const job = existingJob.rows[0];

  if (job.status !== 'failed') {
    throw createError('Can only retry failed jobs', 409);
  }

  // Reset job status
  const result = await query(
    `UPDATE jobs SET status = 'pending', error_message = NULL, progress = 0, 
                     started_at = NULL, completed_at = NULL 
     WHERE id = $1 
     RETURNING id, type, status, progress`,
    [id]
  );

  // Re-add job to BullMQ queue for processing
  const jobType = job.type;
  const payload = job.payload;

  try {
    if (jobType === 'timelapse') {
      await scheduleTimelapseJob({
        jobId: id,
        tenantId: req.user?.tenantId!,
        assetIds: payload.assetIds,
        settings: payload.settings
      });
    } else if (jobType === 'thumbnail') {
      await scheduleThumbnailJob({
        jobId: id,
        tenantId: req.user?.tenantId!,
        assetId: payload.assetId,
        sizes: payload.sizes
      });
    } else if (jobType === 'ftp-sync') {
      await scheduleFtpSyncJob({
        jobId: id,
        tenantId: req.user?.tenantId!,
        ftpSourceId: payload.ftpSourceId,
        syncPath: payload.syncPath
      });
    }
  } catch (queueError) {
    logger.error(`Failed to retry job in queue: ${queueError.message}`);
    throw createError('Failed to retry job', 500);
  }

  logger.info(`Job retried: ${id} by user ${req.user?.id}`);

  res.json({
    job: result.rows[0]
  });
}));

export default router;
