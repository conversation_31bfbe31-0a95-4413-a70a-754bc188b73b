# EagleView MVP

Multi-tenant SaaS platform for timelapse video generation from camera feeds.

## Architecture

- **Frontend**: React + Vite + Tailwind CSS + Auth0
- **Backend API**: Node.js + Express + PostgreSQL + Redis
- **Worker**: BullMQ + FFmpeg for video processing
- **Storage**: S3/MinIO for assets
- **Auth**: Auth0 Organizations for multi-tenant RBAC

## Features

### MVP (Semana 1-6)
- ✅ Multi-tenant architecture with PostgreSQL
- ✅ Auth0 integration for authentication
- ✅ React frontend with modern UI
- ✅ Express API with JWT middleware
- ✅ BullMQ job queue for async processing
- ✅ Docker Compose for local development
- 🔄 FTP image ingestion
- 🔄 FFmpeg video processing
- 🔄 S3/MinIO file storage
- 🔄 Real-time job notifications

### User Roles
- **Admin**: Create tenants, manage users, upload assets, create timelapses
- **Client**: View published assets and timelapses for their organization

## Quick Start

### Prerequisites
- Node.js 18+
- PNPM 8+
- Docker & Docker Compose
- FFmpeg (for video processing)

### Installation

1. **Clone and install dependencies**
```bash
git clone <repository>
cd eagleview-mvp
pnpm install
```

2. **Start infrastructure services**
```bash
pnpm docker:up
```

3. **Configure environment variables**
```bash
# Backend
cp packages/api/.env.example packages/api/.env
# Edit packages/api/.env with your Auth0 credentials

# Frontend  
cp packages/frontend/.env.example packages/frontend/.env
# Edit packages/frontend/.env with your Auth0 credentials
```

4. **Initialize database**
```bash
pnpm db:migrate
```

5. **Start development servers**
```bash
pnpm dev
```

The application will be available at:
- Frontend: http://localhost:3000
- API: http://localhost:3001
- MinIO Console: http://localhost:9001

## Project Structure

```
eagleview-mvp/
├── packages/
│   ├── api/           # Express.js backend
│   ├── frontend/      # React frontend
│   └── worker/        # BullMQ video processing worker
├── docker-compose.yml # Infrastructure services
└── pnpm-workspace.yaml
```

## Development

### Database Schema
- `tenants` - Client organizations
- `users` - User accounts with tenant association
- `assets` - Images and videos with tenant isolation
- `jobs` - Async processing jobs
- `ftp_sources` - FTP configuration per tenant

### API Endpoints
- `POST /api/auth/callback` - Auth0 user sync
- `GET /api/tenants` - Tenant management (admin)
- `GET /api/users` - User management
- `GET /api/assets` - Asset management with tenant filtering
- `GET /api/jobs` - Job status and management

### Job Types
- `timelapse` - Generate MP4 from selected images
- `thumbnail` - Generate thumbnails for images
- `ftp-sync` - Sync images from FTP sources

## Deployment

### Production Environment Variables

**API (.env)**
```bash
NODE_ENV=production
DATABASE_URL=********************************/eagleview
REDIS_URL=redis://host:6379
AUTH0_DOMAIN=your-domain.auth0.com
AUTH0_AUDIENCE=https://api.eagleview.com
AWS_ACCESS_KEY_ID=your-key
AWS_SECRET_ACCESS_KEY=your-secret
S3_BUCKET=eagleview-production
```

**Frontend (.env)**
```bash
VITE_AUTH0_DOMAIN=your-domain.auth0.com
VITE_AUTH0_CLIENT_ID=your-client-id
VITE_AUTH0_AUDIENCE=https://api.eagleview.com
VITE_API_BASE_URL=https://api.eagleview.com
```

### Build and Deploy
```bash
pnpm build
pnpm start
```

## Auth0 Configuration

### Application Settings
- Application Type: Single Page Application
- Allowed Callback URLs: `http://localhost:3000, https://your-domain.com`
- Allowed Logout URLs: `http://localhost:3000, https://your-domain.com`
- Allowed Web Origins: `http://localhost:3000, https://your-domain.com`

### API Settings
- Identifier: `https://api.eagleview.com`
- Signing Algorithm: RS256

### Organizations
Create organizations for each client tenant and assign users accordingly.

## Roadmap

### Phase 1 (MVP - Weeks 1-6)
- [x] Core architecture setup
- [ ] FTP image ingestion
- [ ] Basic timelapse generation
- [ ] User management UI
- [ ] Asset management UI

### Phase 2 (Weeks 7-12)
- [ ] Advanced video editing features
- [ ] Real-time preview with ffmpeg.wasm
- [ ] Batch processing optimization
- [ ] Analytics and reporting
- [ ] Mobile responsive improvements

### Phase 3 (Weeks 13-18)
- [ ] Advanced permissions (ABAC)
- [ ] Multi-camera support
- [ ] AI-powered frame selection
- [ ] CDN integration
- [ ] Advanced monitoring

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

Proprietary - EagleView MVP
