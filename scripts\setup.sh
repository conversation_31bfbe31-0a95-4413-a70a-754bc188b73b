#!/bin/bash

# EagleView MVP Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up EagleView MVP Development Environment"
echo "=================================================="

# Check if required tools are installed
check_dependencies() {
    echo "📋 Checking dependencies..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        echo "❌ Node.js version must be 18 or higher. Current: $(node -v)"
        exit 1
    fi
    echo "✅ Node.js $(node -v)"
    
    # Check PNPM
    if ! command -v pnpm &> /dev/null; then
        echo "📦 Installing PNPM..."
        npm install -g pnpm
    fi
    echo "✅ PNPM $(pnpm -v)"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker is not installed. Please install Docker first."
        exit 1
    fi
    echo "✅ Docker $(docker --version)"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    echo "✅ Docker Compose $(docker-compose --version)"
    
    # Check FFmpeg
    if ! command -v ffmpeg &> /dev/null; then
        echo "⚠️  FFmpeg is not installed. Video processing will not work."
        echo "   Please install FFmpeg for your system:"
        echo "   - macOS: brew install ffmpeg"
        echo "   - Ubuntu: sudo apt install ffmpeg"
        echo "   - Windows: Download from https://ffmpeg.org/download.html"
    else
        echo "✅ FFmpeg $(ffmpeg -version | head -n1 | cut -d' ' -f3)"
    fi
}

# Install dependencies
install_dependencies() {
    echo ""
    echo "📦 Installing dependencies..."
    pnpm install
    echo "✅ Dependencies installed"
}

# Setup environment files
setup_env_files() {
    echo ""
    echo "⚙️  Setting up environment files..."
    
    # API environment
    if [ ! -f "packages/api/.env" ]; then
        cp packages/api/.env.example packages/api/.env
        echo "✅ Created packages/api/.env"
        echo "⚠️  Please update packages/api/.env with your Auth0 credentials"
    else
        echo "✅ packages/api/.env already exists"
    fi
    
    # Frontend environment
    if [ ! -f "packages/frontend/.env" ]; then
        cp packages/frontend/.env.example packages/frontend/.env
        echo "✅ Created packages/frontend/.env"
        echo "⚠️  Please update packages/frontend/.env with your Auth0 credentials"
    else
        echo "✅ packages/frontend/.env already exists"
    fi
}

# Start infrastructure services
start_infrastructure() {
    echo ""
    echo "🐳 Starting infrastructure services..."
    
    # Check if services are already running
    if docker-compose ps | grep -q "Up"; then
        echo "⚠️  Some services are already running. Stopping them first..."
        docker-compose down
    fi
    
    docker-compose up -d
    
    echo "⏳ Waiting for services to be ready..."
    sleep 10
    
    # Check PostgreSQL
    echo "🔍 Checking PostgreSQL connection..."
    for i in {1..30}; do
        if docker-compose exec -T postgres pg_isready -U eagleview > /dev/null 2>&1; then
            echo "✅ PostgreSQL is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "❌ PostgreSQL failed to start"
            exit 1
        fi
        sleep 2
    done
    
    # Check Redis
    echo "🔍 Checking Redis connection..."
    for i in {1..30}; do
        if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
            echo "✅ Redis is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "❌ Redis failed to start"
            exit 1
        fi
        sleep 2
    done
    
    # Check MinIO
    echo "🔍 Checking MinIO connection..."
    for i in {1..30}; do
        if curl -f http://localhost:9000/minio/health/live > /dev/null 2>&1; then
            echo "✅ MinIO is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "❌ MinIO failed to start"
            exit 1
        fi
        sleep 2
    done
}

# Initialize database
init_database() {
    echo ""
    echo "🗄️  Initializing database..."
    
    # The database should be initialized automatically via init.sql
    # But let's verify the tables exist
    TABLES=$(docker-compose exec -T postgres psql -U eagleview -d eagleview -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | tr -d ' ')
    
    if [ "$TABLES" -gt 5 ]; then
        echo "✅ Database initialized with $TABLES tables"
    else
        echo "❌ Database initialization failed"
        exit 1
    fi
}

# Create logs directory
create_logs_dir() {
    echo ""
    echo "📝 Creating logs directory..."
    mkdir -p logs
    echo "✅ Logs directory created"
}

# Display next steps
show_next_steps() {
    echo ""
    echo "🎉 Setup completed successfully!"
    echo "================================"
    echo ""
    echo "📋 Next steps:"
    echo "1. Update Auth0 configuration in .env files:"
    echo "   - packages/api/.env"
    echo "   - packages/frontend/.env"
    echo ""
    echo "2. Start the development servers:"
    echo "   pnpm dev"
    echo ""
    echo "3. Access the application:"
    echo "   - Frontend: http://localhost:3000"
    echo "   - API: http://localhost:3001"
    echo "   - MinIO Console: http://localhost:9001 (eagleview/eagleview_dev_password)"
    echo ""
    echo "4. Check service status:"
    echo "   docker-compose ps"
    echo ""
    echo "5. View logs:"
    echo "   docker-compose logs -f"
    echo ""
    echo "📚 For more information, see README.md"
}

# Main execution
main() {
    check_dependencies
    install_dependencies
    setup_env_files
    start_infrastructure
    init_database
    create_logs_dir
    show_next_steps
}

# Run main function
main
