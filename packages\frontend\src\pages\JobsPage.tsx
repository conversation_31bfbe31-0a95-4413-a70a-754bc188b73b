import { useState } from 'react';
import { useAuthStore } from '../stores/authStore';
import { JobProgress } from '../components/jobs/JobProgress';

export function JobsPage() {
  const { user } = useAuthStore();
  const [filter, setFilter] = useState<'all' | 'pending' | 'processing' | 'completed' | 'failed'>('all');

  return (
    <div>
      <div className="md:flex md:items-center md:justify-between">
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            Jobs
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Monitor processing jobs and their status
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="mt-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'all', label: 'All Jobs' },
              { key: 'processing', label: 'Active' },
              { key: 'completed', label: 'Completed' },
              { key: 'failed', label: 'Failed' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setFilter(tab.key as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  filter === tab.key
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Jobs List */}
      <div className="mt-8">
        <JobProgress showAll={true} />
      </div>
    </div>
  );
}
