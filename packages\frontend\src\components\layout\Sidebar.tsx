import { NavLink } from 'react-router-dom'
import { clsx } from 'clsx'
import { useAuthStore } from '../../stores/authStore'
import {
  HomeIcon,
  PhotoIcon,
  CogIcon,
  UsersIcon,
  BuildingOfficeIcon,
  QueueListIcon,
  ServerIcon,
} from '@heroicons/react/24/outline'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Assets', href: '/assets', icon: PhotoIcon },
  { name: 'Jobs', href: '/jobs', icon: QueueListIcon },
]

const adminNavigation = [
  { name: 'Users', href: '/users', icon: UsersIcon },
  { name: 'Tenants', href: '/tenants', icon: BuildingOfficeIcon },
  { name: 'FTP Sources', href: '/ftp-sources', icon: ServerIcon },
]

export function Sidebar() {
  const { user } = useAuthStore()

  return (
    <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
      <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4 shadow-sm">
        <div className="flex h-16 shrink-0 items-center">
          <h1 className="text-xl font-bold text-gray-900">EagleView</h1>
        </div>
        <nav className="flex flex-1 flex-col">
          <ul role="list" className="flex flex-1 flex-col gap-y-7">
            <li>
              <ul role="list" className="-mx-2 space-y-1">
                {navigation.map((item) => (
                  <li key={item.name}>
                    <NavLink
                      to={item.href}
                      className={({ isActive }) =>
                        clsx(
                          isActive
                            ? 'bg-gray-50 text-primary-600'
                            : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50',
                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'
                        )
                      }
                    >
                      <item.icon
                        className="h-6 w-6 shrink-0"
                        aria-hidden="true"
                      />
                      {item.name}
                    </NavLink>
                  </li>
                ))}
              </ul>
            </li>
            {user?.role === 'admin' && (
              <li>
                <div className="text-xs font-semibold leading-6 text-gray-400">
                  Administration
                </div>
                <ul role="list" className="-mx-2 mt-2 space-y-1">
                  {adminNavigation.map((item) => (
                    <li key={item.name}>
                      <NavLink
                        to={item.href}
                        className={({ isActive }) =>
                          clsx(
                            isActive
                              ? 'bg-gray-50 text-primary-600'
                              : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50',
                            'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'
                          )
                        }
                      >
                        <item.icon
                          className="h-6 w-6 shrink-0"
                          aria-hidden="true"
                        />
                        {item.name}
                      </NavLink>
                    </li>
                  ))}
                </ul>
              </li>
            )}
            <li className="mt-auto">
              <div className="text-xs text-gray-500">
                <div className="font-medium">{user?.name}</div>
                <div>{user?.email}</div>
                <div className="capitalize">{user?.role}</div>
              </div>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  )
}
