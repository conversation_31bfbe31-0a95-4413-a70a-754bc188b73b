import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuthStore } from '../../stores/authStore';
import { useToastStore } from '../../stores/toastStore';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { XMarkIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

const ftpSourceSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255),
  host: z.string().min(1, 'Host is required').max(255),
  port: z.number().min(1).max(65535),
  username: z.string().min(1, 'Username is required').max(255),
  password: z.string().min(1, 'Password is required'),
  path: z.string().default('/'),
  isSecure: z.boolean().default(false),
  syncSchedule: z.string().optional(),
});

type FtpSourceFormData = z.infer<typeof ftpSourceSchema>;

interface FtpSource {
  id: string;
  name: string;
  host: string;
  port: number;
  username: string;
  path: string;
  is_secure: boolean;
  is_active: boolean;
  last_sync?: string;
  created_at: string;
}

interface FtpSourceFormProps {
  ftpSource?: FtpSource;
  onClose: () => void;
  onSave: (ftpSource: FtpSource) => void;
}

export function FtpSourceForm({ ftpSource, onClose, onSave }: FtpSourceFormProps) {
  const { token } = useAuthStore();
  const { success, error } = useToastStore();
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm<FtpSourceFormData>({
    resolver: zodResolver(ftpSourceSchema),
    defaultValues: ftpSource ? {
      name: ftpSource.name,
      host: ftpSource.host,
      port: ftpSource.port,
      username: ftpSource.username,
      password: '', // Don't pre-fill password for security
      path: ftpSource.path,
      isSecure: ftpSource.is_secure,
      syncSchedule: '',
    } : {
      name: '',
      host: '',
      port: 21,
      username: '',
      password: '',
      path: '/',
      isSecure: false,
      syncSchedule: '*/15 * * * *', // Every 15 minutes
    }
  });

  const isSecure = watch('isSecure');

  const onSubmit = async (data: FtpSourceFormData) => {
    setLoading(true);

    try {
      const url = ftpSource ? `/api/ftp-sources/${ftpSource.id}` : '/api/ftp-sources';
      const method = ftpSource ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const result = await response.json();
        success(
          ftpSource ? 'FTP Source Updated' : 'FTP Source Created',
          `Successfully ${ftpSource ? 'updated' : 'created'} FTP source "${data.name}"`
        );
        onSave(result.ftpSource);
        onClose();
      } else {
        const errorData = await response.json();
        error('Failed to Save', errorData.error);
      }
    } catch (err) {
      console.error('Failed to save FTP source:', err);
      error('Connection Error', 'Failed to save FTP source. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    const formData = watch();
    
    if (!formData.host || !formData.username || !formData.password) {
      error('Missing Information', 'Please fill in host, username, and password to test connection');
      return;
    }

    setTesting(true);

    try {
      // For existing FTP sources, use the test endpoint
      if (ftpSource) {
        const response = await fetch(`/api/ftp-sources/${ftpSource.id}/test`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (response.ok) {
          success('Connection Test', 'FTP connection test initiated. Check the Jobs page for results.');
        } else {
          const errorData = await response.json();
          error('Test Failed', errorData.error);
        }
      } else {
        // For new FTP sources, we'd need a separate test endpoint
        error('Test Unavailable', 'Please save the FTP source first, then use the test connection feature.');
      }
    } catch (err) {
      console.error('Failed to test connection:', err);
      error('Test Error', 'Failed to test FTP connection. Please try again.');
    } finally {
      setTesting(false);
    }
  };

  const schedulePresets = [
    { label: 'Every 15 minutes', value: '*/15 * * * *' },
    { label: 'Every 30 minutes', value: '*/30 * * * *' },
    { label: 'Every hour', value: '0 * * * *' },
    { label: 'Every 6 hours', value: '0 */6 * * *' },
    { label: 'Daily at midnight', value: '0 0 * * *' },
    { label: 'Custom', value: 'custom' },
  ];

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900">
            {ftpSource ? 'Edit FTP Source' : 'Add FTP Source'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={loading}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Name *
              </label>
              <input
                {...register('name')}
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Camera 1 FTP"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="host" className="block text-sm font-medium text-gray-700">
                Host *
              </label>
              <input
                {...register('host')}
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="ftp.example.com"
              />
              {errors.host && (
                <p className="mt-1 text-sm text-red-600">{errors.host.message}</p>
              )}
            </div>
          </div>

          {/* Connection Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="port" className="block text-sm font-medium text-gray-700">
                Port *
              </label>
              <input
                {...register('port', { valueAsNumber: true })}
                type="number"
                min="1"
                max="65535"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              {errors.port && (
                <p className="mt-1 text-sm text-red-600">{errors.port.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                Username *
              </label>
              <input
                {...register('username')}
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="ftpuser"
              />
              {errors.username && (
                <p className="mt-1 text-sm text-red-600">{errors.username.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password *
              </label>
              <div className="relative">
                <input
                  {...register('password')}
                  type={showPassword ? 'text' : 'password'}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder={ftpSource ? 'Leave blank to keep current' : 'Enter password'}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-4 w-4 text-gray-400" />
                  ) : (
                    <EyeIcon className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>
          </div>

          {/* Path and Security */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="path" className="block text-sm font-medium text-gray-700">
                Remote Path
              </label>
              <input
                {...register('path')}
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="/images"
              />
              <p className="mt-1 text-xs text-gray-500">
                Directory path on the FTP server to sync from
              </p>
            </div>

            <div className="flex items-center space-x-4 pt-6">
              <label className="flex items-center">
                <input
                  {...register('isSecure')}
                  type="checkbox"
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">
                  Use FTPS/SFTP (Secure)
                </span>
              </label>
            </div>
          </div>

          {/* Sync Schedule */}
          <div>
            <label htmlFor="syncSchedule" className="block text-sm font-medium text-gray-700">
              Sync Schedule (Cron Expression)
            </label>
            <select
              onChange={(e) => {
                if (e.target.value === 'custom') {
                  setValue('syncSchedule', '');
                } else {
                  setValue('syncSchedule', e.target.value);
                }
              }}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              {schedulePresets.map((preset) => (
                <option key={preset.value} value={preset.value}>
                  {preset.label}
                </option>
              ))}
            </select>
            
            <input
              {...register('syncSchedule')}
              type="text"
              className="mt-2 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="*/15 * * * * (every 15 minutes)"
            />
            <p className="mt-1 text-xs text-gray-500">
              Leave empty to disable automatic sync
            </p>
          </div>

          {/* Actions */}
          <div className="flex justify-between items-center pt-6 border-t">
            <div>
              {ftpSource && (
                <button
                  type="button"
                  onClick={testConnection}
                  disabled={testing || loading}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                >
                  {testing && <LoadingSpinner size="sm" className="mr-2" />}
                  Test Connection
                </button>
              )}
            </div>

            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {loading && <LoadingSpinner size="sm" className="mr-2" />}
                {ftpSource ? 'Update' : 'Create'} FTP Source
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
