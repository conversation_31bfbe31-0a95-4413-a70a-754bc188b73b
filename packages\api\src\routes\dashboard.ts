import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { query } from '../database/connection';

const router = Router();

// Get dashboard statistics
router.get('/stats', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const tenantId = req.user?.tenantId;
  const isAdmin = req.user?.role === 'admin';

  try {
    // Base stats for all users
    const [assetsResult, jobsResult, storageResult] = await Promise.all([
      // Assets stats
      query(`
        SELECT 
          COUNT(*) as total_assets,
          COUNT(CASE WHEN type = 'image' THEN 1 END) as total_images,
          COUNT(CASE WHEN type = 'video' THEN 1 END) as total_videos,
          COUNT(CASE WHEN type = 'timelapse' THEN 1 END) as total_timelapses
        FROM assets 
        WHERE tenant_id = $1
      `, [tenantId]),
      
      // Jobs stats
      query(`
        SELECT 
          COUNT(CASE WHEN status IN ('pending', 'processing') THEN 1 END) as active_jobs,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_jobs
        FROM jobs 
        WHERE tenant_id = $1
      `, [tenantId]),
      
      // Storage stats
      query(`
        SELECT 
          COALESCE(SUM(file_size), 0) as storage_used
        FROM assets 
        WHERE tenant_id = $1
      `, [tenantId])
    ]);

    const stats = {
      totalAssets: parseInt(assetsResult.rows[0].total_assets),
      totalImages: parseInt(assetsResult.rows[0].total_images),
      totalVideos: parseInt(assetsResult.rows[0].total_videos),
      totalTimelapses: parseInt(assetsResult.rows[0].total_timelapses),
      activeJobs: parseInt(jobsResult.rows[0].active_jobs),
      completedJobs: parseInt(jobsResult.rows[0].completed_jobs),
      storageUsed: parseInt(storageResult.rows[0].storage_used),
      storageLimit: 100 * 1024 * 1024 * 1024, // 100GB default
    };

    // Add admin-only stats
    if (isAdmin) {
      const [usersResult, tenantsResult, ftpResult] = await Promise.all([
        query('SELECT COUNT(*) as total_users FROM users'),
        query('SELECT COUNT(*) as total_tenants FROM tenants WHERE is_active = true'),
        query('SELECT COUNT(*) as total_ftp_sources FROM ftp_sources WHERE is_active = true')
      ]);

      stats.totalUsers = parseInt(usersResult.rows[0].total_users);
      stats.totalTenants = parseInt(tenantsResult.rows[0].total_tenants);
      stats.totalFtpSources = parseInt(ftpResult.rows[0].total_ftp_sources);
    }

    res.json(stats);
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard stats' });
  }
}));

// Get recent activity
router.get('/activity', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const tenantId = req.user?.tenantId;
  const limit = parseInt(req.query.limit as string) || 10;

  try {
    // Get recent jobs as activity
    const jobsResult = await query(`
      SELECT 
        j.id,
        j.type,
        j.status,
        j.created_at,
        j.completed_at,
        u.name as user_name
      FROM jobs j
      LEFT JOIN users u ON j.created_by = u.id
      WHERE j.tenant_id = $1
      ORDER BY j.created_at DESC
      LIMIT $2
    `, [tenantId, limit]);

    // Get recent assets
    const assetsResult = await query(`
      SELECT 
        a.id,
        a.name,
        a.type,
        a.created_at,
        u.name as user_name
      FROM assets a
      LEFT JOIN users u ON a.created_by = u.id
      WHERE a.tenant_id = $1
      ORDER BY a.created_at DESC
      LIMIT $2
    `, [tenantId, Math.floor(limit / 2)]);

    const activities = [];

    // Convert jobs to activities
    jobsResult.rows.forEach(job => {
      let title = '';
      let description = '';
      let type = 'job_completed';

      switch (job.type) {
        case 'timelapse':
          title = job.status === 'completed' ? 'Timelapse Created' : 'Timelapse Processing';
          description = job.status === 'completed' 
            ? 'Timelapse generation completed successfully'
            : 'Timelapse is being processed';
          type = job.status === 'completed' ? 'timelapse_created' : 'job_processing';
          break;
        case 'thumbnail':
          title = 'Thumbnails Generated';
          description = 'Thumbnail generation completed';
          break;
        case 'ftp-sync':
          title = 'FTP Sync Completed';
          description = 'Synchronized images from FTP source';
          break;
      }

      activities.push({
        id: job.id,
        type,
        title,
        description,
        timestamp: job.completed_at || job.created_at,
        user_name: job.user_name
      });
    });

    // Convert assets to activities
    assetsResult.rows.forEach(asset => {
      activities.push({
        id: asset.id,
        type: 'asset_uploaded',
        title: 'Asset Uploaded',
        description: `${asset.type === 'image' ? 'Image' : 'Video'} "${asset.name}" was uploaded`,
        timestamp: asset.created_at,
        user_name: asset.user_name
      });
    });

    // Sort by timestamp and limit
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    res.json({
      activities: activities.slice(0, limit)
    });
  } catch (error) {
    console.error('Dashboard activity error:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard activity' });
  }
}));

export default router;
