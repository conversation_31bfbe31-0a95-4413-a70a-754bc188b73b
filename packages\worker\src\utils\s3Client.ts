import AWS from 'aws-sdk';
import fs from 'fs-extra';
import path from 'path';
import { logger } from './logger';

// Configure AWS SDK for MinIO or S3
const s3Config: AWS.S3.ClientConfiguration = {
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || 'us-east-1',
};

// If using MinIO (local development)
if (process.env.S3_ENDPOINT) {
  s3Config.endpoint = process.env.S3_ENDPOINT;
  s3Config.s3ForcePathStyle = true;
  s3Config.signatureVersion = 'v4';
}

const s3 = new AWS.S3(s3Config);
const bucketName = process.env.S3_BUCKET || 'eagleview-assets';

/**
 * Upload a file to S3/MinIO
 */
export async function uploadToS3(filePath: string, s3Key: string): Promise<string> {
  try {
    if (!(await fs.pathExists(filePath))) {
      throw new Error(`File not found: ${filePath}`);
    }

    const fileContent = await fs.readFile(filePath);
    const fileStats = await fs.stat(filePath);
    
    // Determine content type based on file extension
    const ext = path.extname(filePath).toLowerCase();
    const contentTypeMap: { [key: string]: string } = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.mp4': 'video/mp4',
      '.mov': 'video/quicktime',
      '.avi': 'video/x-msvideo'
    };
    
    const contentType = contentTypeMap[ext] || 'application/octet-stream';

    const uploadParams: AWS.S3.PutObjectRequest = {
      Bucket: bucketName,
      Key: s3Key,
      Body: fileContent,
      ContentType: contentType,
      ContentLength: fileStats.size,
      Metadata: {
        'uploaded-at': new Date().toISOString(),
        'original-filename': path.basename(filePath)
      }
    };

    // Add cache control for images and videos
    if (contentType.startsWith('image/') || contentType.startsWith('video/')) {
      uploadParams.CacheControl = 'max-age=31536000'; // 1 year
    }

    logger.info(`Uploading to S3: ${s3Key} (${fileStats.size} bytes)`);

    const result = await s3.upload(uploadParams).promise();
    
    logger.info(`Successfully uploaded to S3: ${result.Location}`);
    
    return result.Location;

  } catch (error) {
    logger.error(`Failed to upload to S3: ${s3Key}`, error);
    throw error;
  }
}

/**
 * Download a file from S3/MinIO
 */
export async function downloadFromS3(s3Key: string, localPath: string): Promise<void> {
  try {
    // Ensure local directory exists
    await fs.ensureDir(path.dirname(localPath));

    const downloadParams: AWS.S3.GetObjectRequest = {
      Bucket: bucketName,
      Key: s3Key
    };

    logger.info(`Downloading from S3: ${s3Key}`);

    const result = await s3.getObject(downloadParams).promise();
    
    if (!result.Body) {
      throw new Error('No data received from S3');
    }

    await fs.writeFile(localPath, result.Body as Buffer);
    
    logger.info(`Successfully downloaded from S3: ${localPath}`);

  } catch (error) {
    logger.error(`Failed to download from S3: ${s3Key}`, error);
    throw error;
  }
}

/**
 * Generate a presigned URL for direct access
 */
export async function generatePresignedUrl(s3Key: string, expiresIn: number = 3600): Promise<string> {
  try {
    const params = {
      Bucket: bucketName,
      Key: s3Key,
      Expires: expiresIn
    };

    const url = await s3.getSignedUrlPromise('getObject', params);
    
    logger.info(`Generated presigned URL for: ${s3Key}`);
    
    return url;

  } catch (error) {
    logger.error(`Failed to generate presigned URL: ${s3Key}`, error);
    throw error;
  }
}

/**
 * Delete a file from S3/MinIO
 */
export async function deleteFromS3(s3Key: string): Promise<void> {
  try {
    const deleteParams: AWS.S3.DeleteObjectRequest = {
      Bucket: bucketName,
      Key: s3Key
    };

    logger.info(`Deleting from S3: ${s3Key}`);

    await s3.deleteObject(deleteParams).promise();
    
    logger.info(`Successfully deleted from S3: ${s3Key}`);

  } catch (error) {
    logger.error(`Failed to delete from S3: ${s3Key}`, error);
    throw error;
  }
}

/**
 * List objects in S3/MinIO with prefix
 */
export async function listS3Objects(prefix: string, maxKeys: number = 1000): Promise<AWS.S3.Object[]> {
  try {
    const listParams: AWS.S3.ListObjectsV2Request = {
      Bucket: bucketName,
      Prefix: prefix,
      MaxKeys: maxKeys
    };

    const result = await s3.listObjectsV2(listParams).promise();
    
    return result.Contents || [];

  } catch (error) {
    logger.error(`Failed to list S3 objects with prefix: ${prefix}`, error);
    throw error;
  }
}

/**
 * Check if S3 bucket exists and is accessible
 */
export async function checkS3Connection(): Promise<boolean> {
  try {
    await s3.headBucket({ Bucket: bucketName }).promise();
    logger.info(`S3 bucket '${bucketName}' is accessible`);
    return true;
  } catch (error) {
    logger.error(`S3 bucket '${bucketName}' is not accessible:`, error);
    return false;
  }
}

/**
 * Initialize S3 bucket if it doesn't exist (for MinIO)
 */
export async function initializeS3Bucket(): Promise<void> {
  try {
    // Check if bucket exists
    try {
      await s3.headBucket({ Bucket: bucketName }).promise();
      logger.info(`S3 bucket '${bucketName}' already exists`);
      return;
    } catch (error) {
      // Bucket doesn't exist, create it
      if (error.statusCode === 404) {
        logger.info(`Creating S3 bucket: ${bucketName}`);
        
        await s3.createBucket({ Bucket: bucketName }).promise();
        
        // Set bucket policy for public read access to certain paths
        const bucketPolicy = {
          Version: '2012-10-17',
          Statement: [
            {
              Sid: 'PublicReadGetObject',
              Effect: 'Allow',
              Principal: '*',
              Action: 's3:GetObject',
              Resource: `arn:aws:s3:::${bucketName}/public/*`
            }
          ]
        };

        await s3.putBucketPolicy({
          Bucket: bucketName,
          Policy: JSON.stringify(bucketPolicy)
        }).promise();

        logger.info(`S3 bucket '${bucketName}' created successfully`);
      } else {
        throw error;
      }
    }
  } catch (error) {
    logger.error(`Failed to initialize S3 bucket: ${bucketName}`, error);
    throw error;
  }
}
