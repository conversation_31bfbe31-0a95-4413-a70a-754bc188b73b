import { useState, useEffect } from 'react';
import { useAuthStore } from '../stores/authStore';
import { JobProgress } from '../components/jobs/JobProgress';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import {
  PhotoIcon,
  VideoCameraIcon,
  QueueListIcon,
  UsersIcon,
  BuildingOfficeIcon,
  ServerIcon,
  ChartBarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface DashboardStats {
  totalAssets: number;
  totalImages: number;
  totalVideos: number;
  totalTimelapses: number;
  activeJobs: number;
  completedJobs: number;
  totalUsers?: number;
  totalTenants?: number;
  totalFtpSources?: number;
  storageUsed: number;
  storageLimit: number;
}

interface RecentActivity {
  id: string;
  type: 'asset_uploaded' | 'timelapse_created' | 'job_completed' | 'user_created';
  title: string;
  description: string;
  timestamp: string;
  user_name?: string;
}

export function DashboardPage() {
  const { token, user } = useAuthStore();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsResponse, activityResponse] = await Promise.all([
        fetch('/api/dashboard/stats', {
          headers: { 'Authorization': `Bearer ${token}` },
        }),
        fetch('/api/dashboard/activity', {
          headers: { 'Authorization': `Bearer ${token}` },
        }),
      ]);

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      } else {
        // Fallback to mock data if API fails
        setStats({
          totalAssets: 0,
          totalImages: 0,
          totalVideos: 0,
          totalTimelapses: 0,
          activeJobs: 0,
          completedJobs: 0,
          totalUsers: user?.role === 'admin' ? 0 : undefined,
          totalTenants: user?.role === 'admin' ? 0 : undefined,
          totalFtpSources: user?.role === 'admin' ? 0 : undefined,
          storageUsed: 0,
          storageLimit: 100 * 1024 * 1024 * 1024, // 100 GB
        });
      }

      if (activityResponse.ok) {
        const activityData = await activityResponse.json();
        setRecentActivity(activityData.activities || []);
      } else {
        setRecentActivity([]);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      // Set default stats if API fails
      setStats({
        totalAssets: 0,
        totalImages: 0,
        totalVideos: 0,
        totalTimelapses: 0,
        activeJobs: 0,
        completedJobs: 0,
        totalUsers: user?.role === 'admin' ? 0 : undefined,
        totalTenants: user?.role === 'admin' ? 0 : undefined,
        totalFtpSources: user?.role === 'admin' ? 0 : undefined,
        storageUsed: 0,
        storageLimit: 100 * 1024 * 1024 * 1024, // 100 GB
      });
      setRecentActivity([]);
    } finally {
      setLoading(false);
    }
  };

  const formatStorage = (bytes: number) => {
    const gb = bytes / (1024 * 1024 * 1024);
    if (gb >= 1) return `${gb.toFixed(1)} GB`;
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(0)} MB`;
  };

  const getStoragePercentage = () => {
    if (!stats) return 0;
    return Math.round((stats.storageUsed / stats.storageLimit) * 100);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'asset_uploaded':
        return <PhotoIcon className="h-4 w-4 text-blue-500" />;
      case 'timelapse_created':
        return <VideoCameraIcon className="h-4 w-4 text-green-500" />;
      case 'job_completed':
        return <QueueListIcon className="h-4 w-4 text-purple-500" />;
      case 'user_created':
        return <UsersIcon className="h-4 w-4 text-orange-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div>
      <div className="md:flex md:items-center md:justify-between">
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            Dashboard
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Welcome back, {user?.name}
          </p>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="mt-8">
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {/* Total Assets */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <PhotoIcon className="h-6 w-6 text-blue-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Assets</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats?.totalAssets || 0}</dd>
                  </dl>
                </div>
              </div>
              <div className="mt-3">
                <div className="flex text-xs text-gray-500">
                  <span>{stats?.totalImages || 0} images</span>
                  <span className="mx-2">•</span>
                  <span>{stats?.totalVideos || 0} videos</span>
                </div>
              </div>
            </div>
          </div>

          {/* Timelapses */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <VideoCameraIcon className="h-6 w-6 text-green-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Timelapses</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats?.totalTimelapses || 0}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* Jobs */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <QueueListIcon className="h-6 w-6 text-purple-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Active Jobs</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats?.activeJobs || 0}</dd>
                  </dl>
                </div>
              </div>
              <div className="mt-3">
                <div className="text-xs text-gray-500">
                  {stats?.completedJobs || 0} completed
                </div>
              </div>
            </div>
          </div>

          {/* Storage */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-6 w-6 text-orange-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Storage Used</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {formatStorage(stats?.storageUsed || 0)}
                    </dd>
                  </dl>
                </div>
              </div>
              <div className="mt-3">
                <div className="flex items-center">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        getStoragePercentage() > 80 ? 'bg-red-500' :
                        getStoragePercentage() > 60 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(getStoragePercentage(), 100)}%` }}
                    />
                  </div>
                  <span className="ml-2 text-xs text-gray-500">
                    {getStoragePercentage()}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Admin Stats */}
        {user?.role === 'admin' && (
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-3 mt-5">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <UsersIcon className="h-6 w-6 text-indigo-500" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats?.totalUsers || 0}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <BuildingOfficeIcon className="h-6 w-6 text-pink-500" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Tenants</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats?.totalTenants || 0}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ServerIcon className="h-6 w-6 text-cyan-500" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">FTP Sources</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats?.totalFtpSources || 0}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Content Grid */}
      <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Jobs */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Jobs
            </h3>
            <JobProgress showAll={false} />
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Activity
            </h3>
            {recentActivity.length === 0 ? (
              <div className="text-center py-6">
                <ClockIcon className="mx-auto h-8 w-8 text-gray-400" />
                <p className="mt-2 text-sm text-gray-500">No recent activity</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentActivity.slice(0, 5).map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {activity.title}
                      </p>
                      <p className="text-sm text-gray-500">
                        {activity.description}
                      </p>
                      <p className="text-xs text-gray-400">
                        {formatTimeAgo(activity.timestamp)}
                        {activity.user_name && ` by ${activity.user_name}`}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
