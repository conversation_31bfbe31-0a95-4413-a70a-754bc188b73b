# Auth0 Configuration Guide

Este guia explica como configurar o Auth0 para o EagleView MVP.

## 1. Criar Conta Auth0

1. Acesse [auth0.com](https://auth0.com) e crie uma conta gratuita
2. Crie um novo tenant (ex: `eagleview-dev`)

## 2. Configurar Aplicação SPA (Single Page Application)

### Criar Aplicação
1. No dashboard Auth0, vá em **Applications** > **Create Application**
2. Nome: `EagleView Frontend`
3. Tipo: **Single Page Web Applications**
4. Tecnologia: **React**

### Configurações da Aplicação
```
Application Type: Single Page Application
Token Endpoint Authentication Method: None

Allowed Callback URLs:
http://localhost:3000, https://your-domain.com

Allowed Logout URLs:
http://localhost:3000, https://your-domain.com

Allowed Web Origins:
http://localhost:3000, https://your-domain.com

Allowed Origins (CORS):
http://localhost:3000, https://your-domain.com
```

### Configurações Avançadas
```
Grant Types:
✅ Authorization Code
✅ Refresh Token

OIDC Conformant: ✅ Enabled
```

## 3. Configurar API

### Criar API
1. No dashboard Auth0, vá em **APIs** > **Create API**
2. Nome: `EagleView API`
3. Identifier: `https://api.eagleview.com`
4. Signing Algorithm: `RS256`

### Configurações da API
```
Identifier: https://api.eagleview.com
Signing Algorithm: RS256
Allow Skipping User Consent: ✅ Enabled
Allow Offline Access: ✅ Enabled

Token Expiration: 86400 seconds (24 hours)
Token Expiration For Browser Flows: 7200 seconds (2 hours)
```

### Scopes (Permissões)
Adicione os seguintes scopes na API:

```
read:assets - Read assets
write:assets - Create and update assets
delete:assets - Delete assets
read:jobs - Read jobs
write:jobs - Create and manage jobs
read:users - Read users
write:users - Create and update users
delete:users - Delete users
read:tenants - Read tenants
write:tenants - Create and update tenants
delete:tenants - Delete tenants
read:ftp-sources - Read FTP sources
write:ftp-sources - Create and update FTP sources
delete:ftp-sources - Delete FTP sources
```

## 4. Configurar Organizations (Multi-tenant)

### Habilitar Organizations
1. Vá em **Organizations** no dashboard
2. Clique em **Enable Organizations**

### Criar Organization de Teste
1. Clique em **Create Organization**
2. Nome: `Test Company`
3. Display Name: `Test Company`

### Configurar Roles
1. Vá em **User Management** > **Roles**
2. Crie as seguintes roles:

#### Admin Role
```
Name: Admin
Description: Administrator with full access

Permissions:
- Todas as permissões da API EagleView
```

#### Client Role
```
Name: Client
Description: Client with read-only access

Permissions:
- read:assets
- read:jobs
```

## 5. Configurar Rules/Actions

### Action para adicionar claims customizados
1. Vá em **Actions** > **Flows** > **Login**
2. Crie uma nova Action:

```javascript
exports.onExecutePostLogin = async (event, api) => {
  const namespace = 'https://api.eagleview.com/';
  
  if (event.authorization) {
    // Add custom claims to the token
    api.accessToken.setCustomClaim(`${namespace}email`, event.user.email);
    api.accessToken.setCustomClaim(`${namespace}name`, event.user.name);
    api.accessToken.setCustomClaim(`${namespace}user_id`, event.user.user_id);
    
    // Add organization info if available
    if (event.organization) {
      api.accessToken.setCustomClaim(`${namespace}org_id`, event.organization.id);
      api.accessToken.setCustomClaim(`${namespace}org_name`, event.organization.name);
    }
    
    // Add roles
    if (event.authorization.roles) {
      api.accessToken.setCustomClaim(`${namespace}roles`, event.authorization.roles);
    }
  }
};
```

## 6. Configurar Variáveis de Ambiente

### Backend (.env)
```bash
AUTH0_DOMAIN=your-tenant.auth0.com
AUTH0_AUDIENCE=https://api.eagleview.com
AUTH0_CLIENT_ID=your-spa-client-id
AUTH0_CLIENT_SECRET=your-api-client-secret
```

### Frontend (.env)
```bash
VITE_AUTH0_DOMAIN=your-tenant.auth0.com
VITE_AUTH0_CLIENT_ID=your-spa-client-id
VITE_AUTH0_AUDIENCE=https://api.eagleview.com
```

## 7. Criar Usuários de Teste

### Admin User
1. Vá em **User Management** > **Users**
2. Crie um usuário admin:
```
Email: <EMAIL>
Password: Test123!@#
Email Verified: ✅
```

3. Adicione à organization "Test Company" com role "Admin"

### Client User
1. Crie um usuário client:
```
Email: <EMAIL>
Password: Test123!@#
Email Verified: ✅
```

2. Adicione à organization "Test Company" com role "Client"

## 8. Testar Configuração

### Teste de Login
1. Inicie a aplicação: `pnpm dev`
2. Acesse `http://localhost:3000`
3. Clique em "Sign in"
4. Faça login com os usuários de teste
5. Verifique se o token JWT contém as claims corretas

### Verificar Token JWT
Use [jwt.io](https://jwt.io) para decodificar o token e verificar:
- `aud`: `https://api.eagleview.com`
- `iss`: `https://your-tenant.auth0.com/`
- Claims customizados com namespace

## 9. Troubleshooting

### Erro: "Invalid audience"
- Verifique se `AUTH0_AUDIENCE` está correto
- Confirme que a API foi criada com o identifier correto

### Erro: "Access denied"
- Verifique se o usuário tem as roles corretas
- Confirme que as permissões estão associadas às roles

### Erro: "CORS"
- Adicione `http://localhost:3000` nas configurações de CORS da aplicação Auth0

### Token não contém claims customizados
- Verifique se a Action foi adicionada ao flow de Login
- Confirme que o namespace está correto

## 10. Produção

Para produção, atualize as URLs:
- Callback URLs: `https://your-domain.com`
- Logout URLs: `https://your-domain.com`
- Web Origins: `https://your-domain.com`
- CORS Origins: `https://your-domain.com`

E configure as variáveis de ambiente com os valores de produção.
