import { Router } from 'express';
import { z } from 'zod';
import { asyncHand<PERSON>, createError } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole } from '../middleware/auth';
import { query } from '../database/connection';
import { logger } from '../utils/logger';

const router = Router();

const createUserSchema = z.object({
  email: z.string().email(),
  auth0Id: z.string().min(1),
  name: z.string().min(1).max(255),
  role: z.enum(['admin', 'client']),
  tenantId: z.string().uuid(),
});

const updateUserSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  role: z.enum(['admin', 'client']).optional(),
  isActive: z.boolean().optional(),
});

// Get users (filtered by tenant for clients, all for admins)
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  let queryText = `
    SELECT u.id, u.email, u.name, u.role, u.tenant_id, u.is_active, u.last_login, u.created_at,
           t.name as tenant_name, t.slug as tenant_slug
    FROM users u
    LEFT JOIN tenants t ON u.tenant_id = t.id
  `;
  let queryParams: any[] = [];

  if (req.user?.role === 'client') {
    // Clients can only see users from their own tenant
    queryText += ' WHERE u.tenant_id = $1';
    queryParams.push(req.user.tenantId);
  }

  queryText += ' ORDER BY u.name';

  const result = await query(queryText, queryParams);

  res.json({
    users: result.rows.map(user => ({
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      tenantId: user.tenant_id,
      tenantName: user.tenant_name,
      tenantSlug: user.tenant_slug,
      isActive: user.is_active,
      lastLogin: user.last_login,
      createdAt: user.created_at,
    }))
  });
}));

// Get user by ID
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  
  let queryText = `
    SELECT u.id, u.email, u.name, u.role, u.tenant_id, u.is_active, u.last_login, u.created_at,
           t.name as tenant_name, t.slug as tenant_slug
    FROM users u
    LEFT JOIN tenants t ON u.tenant_id = t.id
    WHERE u.id = $1
  `;
  let queryParams = [id];

  // Clients can only see users from their own tenant
  if (req.user?.role === 'client') {
    queryText += ' AND u.tenant_id = $2';
    queryParams.push(req.user.tenantId);
  }

  const result = await query(queryText, queryParams);

  if (result.rows.length === 0) {
    throw createError('User not found', 404);
  }

  const user = result.rows[0];

  res.json({
    user: {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      tenantId: user.tenant_id,
      tenantName: user.tenant_name,
      tenantSlug: user.tenant_slug,
      isActive: user.is_active,
      lastLogin: user.last_login,
      createdAt: user.created_at,
    }
  });
}));

// Create user (admin only)
router.post('/', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createUserSchema.parse(req.body);

  // Check if user already exists
  const existingUser = await query(
    'SELECT id FROM users WHERE email = $1 OR auth0_id = $2',
    [validatedData.email, validatedData.auth0Id]
  );

  if (existingUser.rows.length > 0) {
    throw createError('User already exists', 409);
  }

  // Check if tenant exists
  const tenant = await query(
    'SELECT id FROM tenants WHERE id = $1',
    [validatedData.tenantId]
  );

  if (tenant.rows.length === 0) {
    throw createError('Tenant not found', 404);
  }

  const result = await query(
    `INSERT INTO users (email, auth0_id, name, role, tenant_id) 
     VALUES ($1, $2, $3, $4, $5) 
     RETURNING id, email, name, role, tenant_id, is_active, created_at`,
    [validatedData.email, validatedData.auth0Id, validatedData.name, validatedData.role, validatedData.tenantId]
  );

  logger.info(`User created: ${validatedData.email} by user ${req.user?.id}`);

  res.status(201).json({
    user: result.rows[0]
  });
}));

// Update user
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const validatedData = updateUserSchema.parse(req.body);

  // Check if user exists and user has permission to update
  let queryText = 'SELECT id, tenant_id FROM users WHERE id = $1';
  let queryParams = [id];

  if (req.user?.role === 'client') {
    // Clients can only update users from their own tenant
    queryText += ' AND tenant_id = $2';
    queryParams.push(req.user.tenantId);
  }

  const existingUser = await query(queryText, queryParams);

  if (existingUser.rows.length === 0) {
    throw createError('User not found', 404);
  }

  // Clients cannot change roles
  if (req.user?.role === 'client' && validatedData.role) {
    throw createError('Insufficient permissions to change user role', 403);
  }

  const updateFields = [];
  const updateValues = [];
  let paramCount = 1;

  if (validatedData.name) {
    updateFields.push(`name = $${paramCount++}`);
    updateValues.push(validatedData.name);
  }

  if (validatedData.role) {
    updateFields.push(`role = $${paramCount++}`);
    updateValues.push(validatedData.role);
  }

  if (validatedData.isActive !== undefined) {
    updateFields.push(`is_active = $${paramCount++}`);
    updateValues.push(validatedData.isActive);
  }

  if (updateFields.length === 0) {
    throw createError('No fields to update', 400);
  }

  updateValues.push(id);

  const result = await query(
    `UPDATE users SET ${updateFields.join(', ')} WHERE id = $${paramCount} 
     RETURNING id, email, name, role, tenant_id, is_active, last_login, created_at`,
    updateValues
  );

  logger.info(`User updated: ${id} by user ${req.user?.id}`);

  res.json({
    user: result.rows[0]
  });
}));

// Delete user (admin only)
router.delete('/:id', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if user exists
  const existingUser = await query(
    'SELECT id, email FROM users WHERE id = $1',
    [id]
  );

  if (existingUser.rows.length === 0) {
    throw createError('User not found', 404);
  }

  // Prevent self-deletion
  if (id === req.user?.id) {
    throw createError('Cannot delete your own account', 409);
  }

  await query('DELETE FROM users WHERE id = $1', [id]);

  logger.info(`User deleted: ${existingUser.rows[0].email} (${id}) by user ${req.user?.id}`);

  res.status(204).send();
}));

export default router;
