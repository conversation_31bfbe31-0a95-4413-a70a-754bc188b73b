import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AssetGrid } from '../assets/AssetGrid';

// Mock the auth store
vi.mock('../../stores/authStore', () => ({
  useAuthStore: () => ({
    token: 'mock-token',
    user: { id: '1', role: 'admin', name: 'Test User' }
  })
}));

// Mock the toast store
vi.mock('../../stores/toastStore', () => ({
  useToastStore: () => ({
    success: vi.fn(),
    error: vi.fn(),
  })
}));

// Mock fetch
global.fetch = vi.fn();

const mockAssets = [
  {
    id: '1',
    name: 'test-image.jpg',
    type: 'image' as const,
    file_path: '/test/image.jpg',
    file_size: 1024000,
    mime_type: 'image/jpeg',
    metadata: {},
    status: 'ready',
    visibility: 'public',
    created_at: '2024-01-01T00:00:00Z',
    created_by_name: 'Test User'
  },
  {
    id: '2',
    name: 'test-video.mp4',
    type: 'video' as const,
    file_path: '/test/video.mp4',
    file_size: 5120000,
    mime_type: 'video/mp4',
    metadata: {},
    status: 'ready',
    visibility: 'private',
    created_at: '2024-01-02T00:00:00Z',
    created_by_name: 'Test User'
  }
];

describe('AssetGrid', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        assets: mockAssets,
        pagination: { page: 1, limit: 20, total: 2, pages: 1 }
      })
    });
  });

  it('renders loading state initially', () => {
    render(<AssetGrid />);
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('renders assets after loading', async () => {
    render(<AssetGrid />);
    
    await waitFor(() => {
      expect(screen.getByText('test-image.jpg')).toBeInTheDocument();
      expect(screen.getByText('test-video.mp4')).toBeInTheDocument();
    });
  });

  it('displays asset metadata correctly', async () => {
    render(<AssetGrid />);
    
    await waitFor(() => {
      expect(screen.getByText('image')).toBeInTheDocument();
      expect(screen.getByText('video')).toBeInTheDocument();
      expect(screen.getByText('public')).toBeInTheDocument();
      expect(screen.getByText('private')).toBeInTheDocument();
    });
  });

  it('formats file sizes correctly', async () => {
    render(<AssetGrid />);
    
    await waitFor(() => {
      expect(screen.getByText('1000.0 KB')).toBeInTheDocument(); // 1024000 bytes
      expect(screen.getByText('4.9 MB')).toBeInTheDocument(); // 5120000 bytes
    });
  });

  it('shows preview button on hover', async () => {
    render(<AssetGrid />);
    
    await waitFor(() => {
      const assetCard = screen.getByText('test-image.jpg').closest('.group');
      expect(assetCard).toBeInTheDocument();
      
      // The preview button should be present but initially hidden
      const previewButton = assetCard?.querySelector('[title="Preview asset"]');
      expect(previewButton).toBeInTheDocument();
    });
  });

  it('handles empty state', async () => {
    (global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        assets: [],
        pagination: { page: 1, limit: 20, total: 0, pages: 0 }
      })
    });

    render(<AssetGrid />);
    
    await waitFor(() => {
      expect(screen.getByText('No assets found')).toBeInTheDocument();
    });
  });

  it('handles API errors', async () => {
    const mockError = vi.fn();
    vi.mocked(require('../../stores/toastStore').useToastStore).mockReturnValue({
      success: vi.fn(),
      error: mockError,
    });

    (global.fetch as any).mockRejectedValue(new Error('API Error'));

    render(<AssetGrid />);
    
    await waitFor(() => {
      expect(mockError).toHaveBeenCalledWith('Failed to Load', 'Could not load assets');
    });
  });

  it('filters assets by type', async () => {
    render(<AssetGrid />);
    
    await waitFor(() => {
      const imageFilter = screen.getByText('Images');
      fireEvent.click(imageFilter);
    });

    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('type=image'),
      expect.any(Object)
    );
  });

  it('calls onPreview when preview button is clicked', async () => {
    const mockOnPreview = vi.fn();
    render(<AssetGrid onPreview={mockOnPreview} />);
    
    await waitFor(() => {
      const previewButton = screen.getAllByTitle('Preview asset')[0];
      fireEvent.click(previewButton);
    });

    expect(mockOnPreview).toHaveBeenCalledWith(mockAssets[0]);
  });

  it('refreshes data when refresh is called', async () => {
    const { rerender } = render(<AssetGrid />);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(1);
    });

    // Simulate refresh
    rerender(<AssetGrid key="refresh" />);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });
  });

  it('shows correct asset icons', async () => {
    render(<AssetGrid />);
    
    await waitFor(() => {
      // Check for image and video icons (by their SVG paths or test IDs)
      const assetCards = screen.getAllByText(/test-/);
      expect(assetCards).toHaveLength(2);
    });
  });

  it('displays creation date and author', async () => {
    render(<AssetGrid />);
    
    await waitFor(() => {
      expect(screen.getByText('Created 1/1/2024')).toBeInTheDocument();
      expect(screen.getByText('Created 1/2/2024')).toBeInTheDocument();
      expect(screen.getAllByText('by Test User')).toHaveLength(2);
    });
  });
});
