import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuthStore } from '../../stores/authStore';
import { useToastStore } from '../../stores/toastStore';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { XMarkIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

const userSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().min(1, 'Name is required').max(255),
  role: z.enum(['admin', 'client']),
  tenantId: z.string().uuid('Invalid tenant ID'),
  isActive: z.boolean().default(true),
  password: z.string().min(8, 'Password must be at least 8 characters').optional(),
});

type UserFormData = z.infer<typeof userSchema>;

interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'client';
  tenant_id: string;
  is_active: boolean;
  created_at: string;
  tenant_name?: string;
}

interface Tenant {
  id: string;
  name: string;
  is_active: boolean;
}

interface UserFormProps {
  user?: User;
  onClose: () => void;
  onSave: (user: User) => void;
}

export function UserForm({ user, onClose, onSave }: UserFormProps) {
  const { token } = useAuthStore();
  const { success, error } = useToastStore();
  const [loading, setLoading] = useState(false);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loadingTenants, setLoadingTenants] = useState(true);
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
    defaultValues: user ? {
      email: user.email,
      name: user.name,
      role: user.role,
      tenantId: user.tenant_id,
      isActive: user.is_active,
      password: undefined, // Don't pre-fill password for security
    } : {
      email: '',
      name: '',
      role: 'client',
      tenantId: '',
      isActive: true,
      password: '',
    }
  });

  useEffect(() => {
    fetchTenants();
  }, []);

  const fetchTenants = async () => {
    try {
      const response = await fetch('/api/tenants', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTenants(data.tenants.filter((t: Tenant) => t.is_active));
        
        // Auto-select first tenant if creating new user
        if (!user && data.tenants.length > 0) {
          setValue('tenantId', data.tenants[0].id);
        }
      }
    } catch (err) {
      console.error('Failed to fetch tenants:', err);
      error('Failed to Load', 'Could not load tenants');
    } finally {
      setLoadingTenants(false);
    }
  };

  const onSubmit = async (data: UserFormData) => {
    setLoading(true);

    try {
      const url = user ? `/api/users/${user.id}` : '/api/users';
      const method = user ? 'PUT' : 'POST';

      // Don't send password if it's empty for existing users
      const payload = { ...data };
      if (user && !data.password) {
        delete payload.password;
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        const result = await response.json();
        success(
          user ? 'User Updated' : 'User Created',
          `Successfully ${user ? 'updated' : 'created'} user "${data.name}"`
        );
        onSave(result.user);
        onClose();
      } else {
        const errorData = await response.json();
        error('Failed to Save', errorData.error);
      }
    } catch (err) {
      console.error('Failed to save user:', err);
      error('Connection Error', 'Failed to save user. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setValue('password', password);
    setShowPassword(true);
  };

  const selectedTenant = tenants.find(t => t.id === watch('tenantId'));

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900">
            {user ? 'Edit User' : 'Add User'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={loading}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Full Name *
              </label>
              <input
                {...register('name')}
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="John Doe"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email Address *
              </label>
              <input
                {...register('email')}
                type="email"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>
          </div>

          {/* Role and Tenant */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="role" className="block text-sm font-medium text-gray-700">
                Role *
              </label>
              <select
                {...register('role')}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="client">Client</option>
                <option value="admin">Admin</option>
              </select>
              {errors.role && (
                <p className="mt-1 text-sm text-red-600">{errors.role.message}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                Admins can manage all resources, clients have read-only access
              </p>
            </div>

            <div>
              <label htmlFor="tenantId" className="block text-sm font-medium text-gray-700">
                Organization *
              </label>
              {loadingTenants ? (
                <div className="mt-1 flex items-center justify-center h-10 border border-gray-300 rounded-md">
                  <LoadingSpinner size="sm" />
                </div>
              ) : (
                <select
                  {...register('tenantId')}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">Select organization...</option>
                  {tenants.map((tenant) => (
                    <option key={tenant.id} value={tenant.id}>
                      {tenant.name}
                    </option>
                  ))}
                </select>
              )}
              {errors.tenantId && (
                <p className="mt-1 text-sm text-red-600">{errors.tenantId.message}</p>
              )}
            </div>
          </div>

          {/* Password */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Password {user ? '(leave blank to keep current)' : '*'}
            </label>
            <div className="mt-1 flex rounded-md shadow-sm">
              <div className="relative flex-1">
                <input
                  {...register('password')}
                  type={showPassword ? 'text' : 'password'}
                  className="block w-full border border-gray-300 rounded-l-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder={user ? 'Leave blank to keep current password' : 'Enter password'}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-4 w-4 text-gray-400" />
                  ) : (
                    <EyeIcon className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
              <button
                type="button"
                onClick={generatePassword}
                className="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 text-sm hover:bg-gray-100"
              >
                Generate
              </button>
            </div>
            {errors.password && (
              <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Password must be at least 8 characters long
            </p>
          </div>

          {/* Status */}
          <div className="flex items-center">
            <input
              {...register('isActive')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
              Active user
            </label>
          </div>

          {/* Preview */}
          {selectedTenant && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">User Preview</h4>
              <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                <div>
                  <dt className="text-blue-700">Name:</dt>
                  <dd className="text-blue-900 font-medium">{watch('name') || 'Not set'}</dd>
                </div>
                <div>
                  <dt className="text-blue-700">Email:</dt>
                  <dd className="text-blue-900 font-medium">{watch('email') || 'Not set'}</dd>
                </div>
                <div>
                  <dt className="text-blue-700">Role:</dt>
                  <dd className="text-blue-900 font-medium capitalize">{watch('role')}</dd>
                </div>
                <div>
                  <dt className="text-blue-700">Organization:</dt>
                  <dd className="text-blue-900 font-medium">{selectedTenant.name}</dd>
                </div>
              </dl>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || loadingTenants}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {loading && <LoadingSpinner size="sm" className="mr-2" />}
              {user ? 'Update' : 'Create'} User
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
