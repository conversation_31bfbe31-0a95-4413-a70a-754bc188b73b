import { Job } from 'bullmq';
import ffmpeg from 'fluent-ffmpeg';
import path from 'path';
import fs from 'fs-extra';
import { logger } from '../utils/logger';
import { query } from '../database/connection';
import { uploadToS3 } from '../utils/s3Client';

interface TimelapseJobData {
  jobId: string;
  tenantId: string;
  assetIds: string[];
  settings: {
    fps: number;
    quality: 'high' | 'medium' | 'low';
    resolution: '4k' | '1080p' | '720p';
    outputName: string;
  };
}

export async function processTimelapseJob(job: Job<TimelapseJobData>) {
  const { jobId, tenantId, assetIds, settings } = job.data;
  
  logger.info(`Starting timelapse job ${jobId} for tenant ${tenantId}`);
  
  try {
    // Update job status to processing
    await updateJobStatus(jobId, 'processing', 0);
    
    // Get asset file paths from database
    const assetsResult = await query(
      'SELECT id, file_path, name FROM assets WHERE id = ANY($1) AND tenant_id = $2 ORDER BY created_at',
      [assetIds, tenantId]
    );
    
    if (assetsResult.rows.length === 0) {
      throw new Error('No assets found for timelapse generation');
    }
    
    const assets = assetsResult.rows;
    logger.info(`Processing ${assets.length} assets for timelapse`);
    
    // Create temporary directory for processing
    const tempDir = path.join(__dirname, '../../temp', jobId);
    await fs.ensureDir(tempDir);
    
    // Download assets from S3 to temp directory
    await job.updateProgress(10);
    const localPaths: string[] = [];
    
    for (let i = 0; i < assets.length; i++) {
      const asset = assets[i];
      const localPath = path.join(tempDir, `frame_${i.toString().padStart(6, '0')}.jpg`);
      
      // TODO: Download from S3 - for now assume local files
      // await downloadFromS3(asset.file_path, localPath);
      
      // For MVP, copy from uploads directory
      const sourcePath = path.join(__dirname, '../../..', asset.file_path);
      if (await fs.pathExists(sourcePath)) {
        await fs.copy(sourcePath, localPath);
        localPaths.push(localPath);
      }
      
      await job.updateProgress(10 + (i / assets.length) * 30);
    }
    
    if (localPaths.length === 0) {
      throw new Error('No valid asset files found');
    }
    
    // Generate timelapse video
    await job.updateProgress(40);
    const outputPath = await generateTimelapse(tempDir, settings, job);
    
    // Upload result to S3
    await job.updateProgress(80);
    const s3Key = `timelapses/${tenantId}/${jobId}/${settings.outputName}.mp4`;
    const s3Url = await uploadToS3(outputPath, s3Key);
    
    // Create asset record for the generated timelapse
    const assetResult = await query(
      `INSERT INTO assets (tenant_id, name, type, file_path, file_size, mime_type, metadata, visibility, created_by) 
       VALUES ($1, $2, 'timelapse', $3, $4, 'video/mp4', $5, 'private', 
               (SELECT created_by FROM jobs WHERE id = $6)) 
       RETURNING id`,
      [
        tenantId,
        settings.outputName,
        s3Key,
        (await fs.stat(outputPath)).size,
        JSON.stringify({
          fps: settings.fps,
          quality: settings.quality,
          resolution: settings.resolution,
          frameCount: localPaths.length,
          duration: localPaths.length / settings.fps
        }),
        jobId
      ]
    );
    
    // Update job with result
    await updateJobStatus(jobId, 'completed', 100, {
      assetId: assetResult.rows[0].id,
      s3Url,
      frameCount: localPaths.length,
      duration: localPaths.length / settings.fps
    });
    
    // Cleanup temp directory
    await fs.remove(tempDir);
    
    logger.info(`Timelapse job ${jobId} completed successfully`);
    
    // Notify via Socket.IO
    if (global.io) {
      global.io.to(`tenant-${tenantId}`).emit('job-completed', {
        jobId,
        type: 'timelapse',
        assetId: assetResult.rows[0].id
      });
    }
    
  } catch (error) {
    logger.error(`Timelapse job ${jobId} failed:`, error);
    
    await updateJobStatus(jobId, 'failed', job.progress || 0, null, error.message);
    
    // Notify via Socket.IO
    if (global.io) {
      global.io.to(`tenant-${tenantId}`).emit('job-failed', {
        jobId,
        type: 'timelapse',
        error: error.message
      });
    }
    
    throw error;
  }
}

async function generateTimelapse(
  tempDir: string, 
  settings: TimelapseJobData['settings'],
  job: Job
): Promise<string> {
  const outputPath = path.join(tempDir, `${settings.outputName}.mp4`);
  const inputPattern = path.join(tempDir, 'frame_%06d.jpg');
  
  // Resolution settings
  const resolutionMap = {
    '4k': { width: 3840, height: 2160 },
    '1080p': { width: 1920, height: 1080 },
    '720p': { width: 1280, height: 720 }
  };
  
  const resolution = resolutionMap[settings.resolution];
  
  // Quality settings (CRF values)
  const qualityMap = {
    'high': 18,
    'medium': 23,
    'low': 28
  };
  
  const crf = qualityMap[settings.quality];
  
  return new Promise((resolve, reject) => {
    ffmpeg(inputPattern)
      .inputFPS(settings.fps)
      .videoCodec('libx264')
      .outputOptions([
        `-crf ${crf}`,
        '-pix_fmt yuv420p',
        '-movflags +faststart', // Optimize for streaming
        `-vf scale=${resolution.width}:${resolution.height}:force_original_aspect_ratio=decrease,pad=${resolution.width}:${resolution.height}:(ow-iw)/2:(oh-ih)/2`
      ])
      .output(outputPath)
      .on('start', (commandLine) => {
        logger.info(`FFmpeg command: ${commandLine}`);
      })
      .on('progress', async (progress) => {
        const percent = Math.round(progress.percent || 0);
        await job.updateProgress(40 + (percent * 0.4)); // 40-80% range
        logger.info(`FFmpeg progress: ${percent}%`);
      })
      .on('end', () => {
        logger.info(`Timelapse generation completed: ${outputPath}`);
        resolve(outputPath);
      })
      .on('error', (error) => {
        logger.error('FFmpeg error:', error);
        reject(error);
      })
      .run();
  });
}

async function updateJobStatus(
  jobId: string, 
  status: string, 
  progress: number, 
  result?: any, 
  errorMessage?: string
) {
  const updateFields = ['status = $2', 'progress = $3'];
  const updateValues = [jobId, status, progress];
  let paramCount = 4;
  
  if (status === 'processing' && !errorMessage) {
    updateFields.push('started_at = NOW()');
  }
  
  if (status === 'completed' || status === 'failed') {
    updateFields.push('completed_at = NOW()');
  }
  
  if (result) {
    updateFields.push(`result = $${paramCount++}`);
    updateValues.push(JSON.stringify(result));
  }
  
  if (errorMessage) {
    updateFields.push(`error_message = $${paramCount++}`);
    updateValues.push(errorMessage);
  }
  
  await query(
    `UPDATE jobs SET ${updateFields.join(', ')} WHERE id = $1`,
    updateValues
  );
}
