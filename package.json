{"name": "eagleview-mvp", "version": "1.0.0", "description": "EagleView MVP - Multi-tenant SaaS for timelapse video generation", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "concurrently \"pnpm --filter api dev\" \"pnpm --filter frontend dev\" \"pnpm --filter worker dev\"", "build": "pnpm --filter api build && pnpm --filter frontend build && pnpm --filter worker build", "start": "pnpm --filter api start", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "db:migrate": "pnpm --filter api db:migrate", "db:seed": "pnpm --filter api db:seed", "test": "pnpm --recursive test", "lint": "pnpm --recursive lint", "clean": "pnpm --recursive clean"}, "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}