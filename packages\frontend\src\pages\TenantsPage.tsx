import { useState, useEffect } from 'react';
import { useAuthStore } from '../stores/authStore';
import { useToastStore } from '../stores/toastStore';
import { TenantForm } from '../components/tenants/TenantForm';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import {
  PlusIcon,
  BuildingOfficeIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  UsersIcon,
  ServerIcon
} from '@heroicons/react/24/outline';

interface Tenant {
  id: string;
  name: string;
  display_name: string;
  domain?: string;
  description?: string;
  settings: any;
  is_active: boolean;
  created_at: string;
  user_count?: number;
  storage_used?: number;
}

export function TenantsPage() {
  const { token, user: currentUser } = useAuthStore();
  const { success, error } = useToastStore();
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingTenant, setEditingTenant] = useState<Tenant | undefined>();
  const [filter, setFilter] = useState<'all' | 'active' | 'inactive'>('all');

  useEffect(() => {
    fetchTenants();
  }, []);

  const fetchTenants = async () => {
    try {
      const response = await fetch('/api/tenants', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTenants(data.tenants);
      }
    } catch (err) {
      console.error('Failed to fetch tenants:', err);
      error('Failed to Load', 'Could not load tenants');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = (tenant: Tenant) => {
    if (editingTenant) {
      setTenants(prev => prev.map(t => t.id === tenant.id ? tenant : t));
    } else {
      setTenants(prev => [tenant, ...prev]);
    }
    setEditingTenant(undefined);
  };

  const handleEdit = (tenant: Tenant) => {
    setEditingTenant(tenant);
    setShowForm(true);
  };

  const handleDelete = async (tenant: Tenant) => {
    if (!confirm(`Are you sure you want to delete "${tenant.display_name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/tenants/${tenant.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        setTenants(prev => prev.filter(t => t.id !== tenant.id));
        success('Tenant Deleted', `Successfully deleted "${tenant.display_name}"`);
      } else {
        const errorData = await response.json();
        error('Delete Failed', errorData.error);
      }
    } catch (err) {
      console.error('Failed to delete tenant:', err);
      error('Delete Error', 'Failed to delete tenant');
    }
  };

  const toggleTenantStatus = async (tenant: Tenant) => {
    try {
      const response = await fetch(`/api/tenants/${tenant.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: !tenant.is_active
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setTenants(prev => prev.map(t => t.id === tenant.id ? data.tenant : t));
        success(
          'Tenant Updated',
          `${tenant.display_name} is now ${!tenant.is_active ? 'active' : 'inactive'}`
        );
      } else {
        const errorData = await response.json();
        error('Update Failed', errorData.error);
      }
    } catch (err) {
      console.error('Failed to update tenant:', err);
      error('Update Error', 'Failed to update tenant status');
    }
  };

  const filteredTenants = tenants.filter(tenant => {
    switch (filter) {
      case 'active': return tenant.is_active;
      case 'inactive': return !tenant.is_active;
      default: return true;
    }
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatStorage = (bytes?: number) => {
    if (!bytes) return '0 MB';
    const gb = bytes / (1024 * 1024 * 1024);
    if (gb >= 1) return `${gb.toFixed(1)} GB`;
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(0)} MB`;
  };

  if (currentUser?.role !== 'admin') {
    return (
      <div className="text-center py-12">
        <XCircleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-semibold text-gray-900">Access Denied</h3>
        <p className="mt-1 text-sm text-gray-500">
          You need admin privileges to manage tenants.
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="md:flex md:items-center md:justify-between">
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            Tenants
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Manage client organizations and their settings
          </p>
        </div>
        <div className="mt-4 flex md:ml-4 md:mt-0">
          <button
            onClick={() => {
              setEditingTenant(undefined);
              setShowForm(true);
            }}
            type="button"
            className="inline-flex items-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Tenant
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="mt-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'all', label: 'All Tenants' },
              { key: 'active', label: 'Active' },
              { key: 'inactive', label: 'Inactive' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setFilter(tab.key as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  filter === tab.key
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      <div className="mt-8">
        {loading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : filteredTenants.length === 0 ? (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="text-center">
                <BuildingOfficeIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-semibold text-gray-900">
                  {filter === 'all' ? 'No tenants' : `No ${filter} tenants`}
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {filter === 'all'
                    ? 'Get started by creating your first tenant organization.'
                    : `No tenants match the ${filter} filter.`
                  }
                </p>
                {filter === 'all' && (
                  <div className="mt-6">
                    <button
                      onClick={() => {
                        setEditingTenant(undefined);
                        setShowForm(true);
                      }}
                      type="button"
                      className="inline-flex items-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Add Tenant
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {filteredTenants.map((tenant) => (
              <div
                key={tenant.id}
                className="bg-white overflow-hidden shadow rounded-lg"
              >
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <BuildingOfficeIcon className="h-8 w-8 text-gray-400" />
                    </div>
                    <div className="ml-4 flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {tenant.display_name}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {tenant.name}
                      </p>
                    </div>
                    <div className="flex-shrink-0">
                      {tenant.is_active ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                  </div>

                  {tenant.description && (
                    <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                      {tenant.description}
                    </p>
                  )}

                  <div className="mt-4">
                    <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                      <div className="flex items-center">
                        <UsersIcon className="h-4 w-4 text-gray-400 mr-1" />
                        <span className="text-gray-500">Users:</span>
                        <span className="ml-1 text-gray-900">
                          {tenant.user_count || 0}/{tenant.settings?.maxUsers || 10}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <ServerIcon className="h-4 w-4 text-gray-400 mr-1" />
                        <span className="text-gray-500">Storage:</span>
                        <span className="ml-1 text-gray-900">
                          {formatStorage(tenant.storage_used)}/{tenant.settings?.maxStorage || 100}GB
                        </span>
                      </div>
                      {tenant.domain && (
                        <div className="col-span-2">
                          <span className="text-gray-500">Domain:</span>
                          <span className="ml-1 text-gray-900">{tenant.domain}</span>
                        </div>
                      )}
                      <div className="col-span-2">
                        <span className="text-gray-500">Created:</span>
                        <span className="ml-1 text-gray-900">{formatDate(tenant.created_at)}</span>
                      </div>
                    </dl>
                  </div>

                  <div className="mt-6 flex justify-between">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => toggleTenantStatus(tenant)}
                        className={`text-xs px-2 py-1 rounded ${
                          tenant.is_active
                            ? 'text-red-600 hover:text-red-700'
                            : 'text-green-600 hover:text-green-700'
                        }`}
                      >
                        {tenant.is_active ? 'Deactivate' : 'Activate'}
                      </button>
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(tenant)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>

                      <button
                        onClick={() => handleDelete(tenant)}
                        className="text-gray-400 hover:text-red-600"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  {/* Feature indicators */}
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="flex space-x-4 text-xs">
                      <span className={`px-2 py-1 rounded ${
                        tenant.settings?.allowFtpSources !== false
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        FTP Sources
                      </span>
                      <span className={`px-2 py-1 rounded ${
                        tenant.settings?.allowTimelapses !== false
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        Timelapses
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Form Modal */}
      {showForm && (
        <TenantForm
          tenant={editingTenant}
          onClose={() => {
            setShowForm(false);
            setEditingTenant(undefined);
          }}
          onSave={handleSave}
        />
      )}
    </div>
  );
}
