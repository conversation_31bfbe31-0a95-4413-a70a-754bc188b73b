import { Job } from 'bullmq';
import { Client as FTPClient } from 'basic-ftp';
import path from 'path';
import fs from 'fs-extra';
import { logger } from '../utils/logger';
import { query } from '../database/connection';
import { uploadToS3 } from '../utils/s3Client';
import crypto from 'crypto';

interface FtpSyncJobData {
  jobId: string;
  tenantId: string;
  ftpSourceId: string;
  syncPath?: string; // Optional specific path to sync
}

interface FtpSource {
  id: string;
  name: string;
  host: string;
  port: number;
  username: string;
  password_encrypted: string;
  path: string;
  is_secure: boolean;
  last_sync: string | null;
}

export async function processFtpSyncJob(job: Job<FtpSyncJobData>) {
  const { jobId, tenantId, ftpSourceId, syncPath } = job.data;
  
  logger.info(`Starting FTP sync job ${jobId} for source ${ftpSourceId}`);
  
  try {
    // Update job status to processing
    await updateJobStatus(jobId, 'processing', 0);
    
    // Get FTP source configuration
    const ftpResult = await query(
      'SELECT * FROM ftp_sources WHERE id = $1 AND tenant_id = $2 AND is_active = true',
      [ftpSourceId, tenantId]
    );
    
    if (ftpResult.rows.length === 0) {
      throw new Error('FTP source not found or inactive');
    }
    
    const ftpSource: FtpSource = ftpResult.rows[0];
    
    // Decrypt password (in production, use proper encryption)
    const password = decryptPassword(ftpSource.password_encrypted);
    
    // Create temporary directory for downloads
    const tempDir = path.join(__dirname, '../../temp', jobId);
    await fs.ensureDir(tempDir);
    
    // Connect to FTP server
    const client = new FTPClient();
    client.ftp.verbose = process.env.NODE_ENV === 'development';
    
    try {
      await client.access({
        host: ftpSource.host,
        port: ftpSource.port,
        user: ftpSource.username,
        password: password,
        secure: ftpSource.is_secure
      });
      
      logger.info(`Connected to FTP server: ${ftpSource.host}`);
      await job.updateProgress(10);
      
      // Navigate to the specified path
      const remotePath = syncPath || ftpSource.path;
      await client.cd(remotePath);
      
      // List files in the directory
      const fileList = await client.list();
      const imageFiles = fileList.filter(file => 
        file.isFile && 
        /\.(jpg|jpeg|png)$/i.test(file.name) &&
        file.size > 0
      );
      
      logger.info(`Found ${imageFiles.length} image files to sync`);
      
      if (imageFiles.length === 0) {
        await updateJobStatus(jobId, 'completed', 100, {
          message: 'No new images found',
          filesProcessed: 0
        });
        return;
      }
      
      // Check which files are new (not already in database)
      const existingFiles = await query(
        `SELECT metadata->>'originalFilename' as filename 
         FROM assets 
         WHERE tenant_id = $1 AND type = 'image' 
         AND metadata->>'ftpSourceId' = $2`,
        [tenantId, ftpSourceId]
      );
      
      const existingFilenames = new Set(
        existingFiles.rows.map(row => row.filename).filter(Boolean)
      );
      
      const newFiles = imageFiles.filter(file => 
        !existingFilenames.has(file.name)
      );
      
      logger.info(`${newFiles.length} new files to download`);
      
      const processedFiles: Array<{
        filename: string;
        assetId: string;
        s3Url: string;
        size: number;
      }> = [];
      
      // Download and process each new file
      for (let i = 0; i < newFiles.length; i++) {
        const file = newFiles[i];
        const localPath = path.join(tempDir, file.name);
        
        try {
          // Download file
          await client.downloadTo(localPath, file.name);
          logger.info(`Downloaded: ${file.name} (${file.size} bytes)`);
          
          // Generate unique filename for S3
          const fileExt = path.extname(file.name);
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          const hash = crypto.createHash('md5').update(file.name + file.modifiedAt).digest('hex').substring(0, 8);
          const s3Filename = `${timestamp}-${hash}${fileExt}`;
          
          // Upload to S3
          const s3Key = `images/${tenantId}/${s3Filename}`;
          const s3Url = await uploadToS3(localPath, s3Key);
          
          // Create asset record
          const assetResult = await query(
            `INSERT INTO assets (tenant_id, name, type, file_path, file_size, mime_type, metadata, status, visibility) 
             VALUES ($1, $2, 'image', $3, $4, $5, $6, 'active', 'private') 
             RETURNING id`,
            [
              tenantId,
              file.name,
              s3Key,
              file.size,
              `image/${fileExt.substring(1)}`,
              JSON.stringify({
                originalFilename: file.name,
                ftpSourceId: ftpSourceId,
                ftpSourceName: ftpSource.name,
                downloadedAt: new Date().toISOString(),
                modifiedAt: file.modifiedAt?.toISOString(),
                remotePath: path.join(remotePath, file.name)
              })
            ]
          );
          
          const assetId = assetResult.rows[0].id;
          
          processedFiles.push({
            filename: file.name,
            assetId,
            s3Url,
            size: file.size
          });
          
          // Schedule thumbnail generation
          await scheduleJob('thumbnail', {
            jobId: crypto.randomUUID(),
            tenantId,
            assetId,
            sizes: [
              { name: 'thumb', width: 150, height: 150, quality: 80 },
              { name: 'medium', width: 400, height: 300, quality: 85 },
              { name: 'large', width: 800, height: 600, quality: 90 }
            ]
          });
          
          // Clean up local file
          await fs.remove(localPath);
          
          await job.updateProgress(10 + ((i + 1) / newFiles.length) * 80);
          
        } catch (fileError) {
          logger.error(`Failed to process file ${file.name}:`, fileError);
          // Continue with other files
        }
      }
      
      // Update last sync timestamp
      await query(
        'UPDATE ftp_sources SET last_sync = NOW() WHERE id = $1',
        [ftpSourceId]
      );
      
      // Update job with result
      await updateJobStatus(jobId, 'completed', 100, {
        filesProcessed: processedFiles.length,
        totalFilesFound: imageFiles.length,
        newFilesFound: newFiles.length,
        processedFiles: processedFiles.map(f => ({
          filename: f.filename,
          assetId: f.assetId,
          size: f.size
        }))
      });
      
      logger.info(`FTP sync job ${jobId} completed: ${processedFiles.length} files processed`);
      
      // Notify via Socket.IO
      if (global.io) {
        global.io.to(`tenant-${tenantId}`).emit('job-completed', {
          jobId,
          type: 'ftp-sync',
          filesProcessed: processedFiles.length
        });
      }
      
    } finally {
      client.close();
    }
    
    // Cleanup temp directory
    await fs.remove(tempDir);
    
  } catch (error) {
    logger.error(`FTP sync job ${jobId} failed:`, error);
    
    await updateJobStatus(jobId, 'failed', job.progress || 0, null, error.message);
    
    // Notify via Socket.IO
    if (global.io) {
      global.io.to(`tenant-${tenantId}`).emit('job-failed', {
        jobId,
        type: 'ftp-sync',
        error: error.message
      });
    }
    
    throw error;
  }
}

function decryptPassword(encryptedPassword: string): string {
  // TODO: Implement proper encryption/decryption
  // For MVP, assume base64 encoding
  try {
    return Buffer.from(encryptedPassword, 'base64').toString('utf8');
  } catch {
    return encryptedPassword; // Fallback to plain text for development
  }
}

async function scheduleJob(queueName: string, jobData: any) {
  // TODO: Add job to BullMQ queue
  // This would be implemented when we have the queue setup
  logger.info(`Scheduling ${queueName} job:`, jobData);
}

async function updateJobStatus(
  jobId: string, 
  status: string, 
  progress: number, 
  result?: any, 
  errorMessage?: string
) {
  const updateFields = ['status = $2', 'progress = $3'];
  const updateValues = [jobId, status, progress];
  let paramCount = 4;
  
  if (status === 'processing' && !errorMessage) {
    updateFields.push('started_at = NOW()');
  }
  
  if (status === 'completed' || status === 'failed') {
    updateFields.push('completed_at = NOW()');
  }
  
  if (result) {
    updateFields.push(`result = $${paramCount++}`);
    updateValues.push(JSON.stringify(result));
  }
  
  if (errorMessage) {
    updateFields.push(`error_message = $${paramCount++}`);
    updateValues.push(errorMessage);
  }
  
  await query(
    `UPDATE jobs SET ${updateFields.join(', ')} WHERE id = $1`,
    updateValues
  );
}
